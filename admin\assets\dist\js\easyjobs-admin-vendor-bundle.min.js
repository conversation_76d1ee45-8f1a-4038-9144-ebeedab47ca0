"use strict";function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}!function(t,e){"object"===("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?e(exports,require("jquery")):"function"==typeof define&&define.amd?define(["exports","jquery"],e):e((t=t||self).bootstrap={},t.jQuery)}(void 0,function(t,p){function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function l(o){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},e=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(e=e.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),e.forEach(function(t){var e,n,i;e=o,i=r[n=t],n in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i})}return o}p=p&&p.hasOwnProperty("default")?p.default:p;var e="transitionend";function n(t){var e=this,n=!1;return p(this).one(m.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||m.triggerTransitionEnd(e)},t),this}var m={TRANSITION_END:"bsTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(t){var e,n=t.getAttribute("data-target");n&&"#"!==n||(n=(e=t.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(n)?n:null}catch(t){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var e=p(t).css("transition-duration"),n=p(t).css("transition-delay"),i=parseFloat(e),o=parseFloat(n);return i||o?(e=e.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(e)+parseFloat(n))):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){p(t).trigger(e)},supportsTransitionEnd:function(){return Boolean(e)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var o=n[i],r=e[i],s=r&&m.isElement(r)?"element":(a=r,{}.toString.call(a).match(/\s([a-z]+)/i)[1].toLowerCase());if(!new RegExp(o).test(s))throw new Error(t.toUpperCase()+': Option "'+i+'" provided type "'+s+'" but expected type "'+o+'".')}var a},findShadowRoot:function(t){if(!document.documentElement.attachShadow)return null;if("function"!=typeof t.getRootNode)return t instanceof ShadowRoot?t:t.parentNode?m.findShadowRoot(t.parentNode):null;var e=t.getRootNode();return e instanceof ShadowRoot?e:null}};p.fn.emulateTransitionEnd=n,p.event.special[m.TRANSITION_END]={bindType:e,delegateType:e,handle:function(t){if(p(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}};var o="alert",r="bs.alert",a="."+r,c=p.fn[o],h={CLOSE:"close"+a,CLOSED:"closed"+a,CLICK_DATA_API:"click"+a+".data-api"},u="alert",f="fade",d="show",g=function(){function i(t){this._element=t}var t=i.prototype;return t.close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},t.dispose=function(){p.removeData(this._element,r),this._element=null},t._getRootElement=function(t){var e=m.getSelectorFromElement(t),n=!1;return e&&(n=document.querySelector(e)),n=n||p(t).closest("."+u)[0]},t._triggerCloseEvent=function(t){var e=p.Event(h.CLOSE);return p(t).trigger(e),e},t._removeElement=function(e){var t,n=this;p(e).removeClass(d),p(e).hasClass(f)?(t=m.getTransitionDurationFromElement(e),p(e).one(m.TRANSITION_END,function(t){return n._destroyElement(e,t)}).emulateTransitionEnd(t)):this._destroyElement(e)},t._destroyElement=function(t){p(t).detach().trigger(h.CLOSED).remove()},i._jQueryInterface=function(n){return this.each(function(){var t=p(this),e=t.data(r);e||(e=new i(this),t.data(r,e)),"close"===n&&e[n](this)})},i._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},s(i,null,[{key:"VERSION",get:function(){return"4.3.1"}}]),i}();p(document).on(h.CLICK_DATA_API,'[data-dismiss="alert"]',g._handleDismiss(new g)),p.fn[o]=g._jQueryInterface,p.fn[o].Constructor=g,p.fn[o].noConflict=function(){return p.fn[o]=c,g._jQueryInterface};var _="button",v="bs.button",y="."+v,E=".data-api",b=p.fn[_],w="active",C="btn",T="focus",S='[data-toggle^="button"]',D='[data-toggle="buttons"]',I='input:not([type="hidden"])',A=".active",O=".btn",N={CLICK_DATA_API:"click"+y+E,FOCUS_BLUR_DATA_API:"focus"+y+E+" blur"+y+E},k=function(){function n(t){this._element=t}var t=n.prototype;return t.toggle=function(){var t=!0,e=!0,n=p(this._element).closest(D)[0];if(n){var i,o=this._element.querySelector(I);if(o){if("radio"===o.type&&(o.checked&&this._element.classList.contains(w)?t=!1:(i=n.querySelector(A))&&p(i).removeClass(w)),t){if(o.hasAttribute("disabled")||n.hasAttribute("disabled")||o.classList.contains("disabled")||n.classList.contains("disabled"))return;o.checked=!this._element.classList.contains(w),p(o).trigger("change")}o.focus(),e=!1}}e&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(w)),t&&p(this._element).toggleClass(w)},t.dispose=function(){p.removeData(this._element,v),this._element=null},n._jQueryInterface=function(e){return this.each(function(){var t=p(this).data(v);t||(t=new n(this),p(this).data(v,t)),"toggle"===e&&t[e]()})},s(n,null,[{key:"VERSION",get:function(){return"4.3.1"}}]),n}();p(document).on(N.CLICK_DATA_API,S,function(t){t.preventDefault();var e=t.target;p(e).hasClass(C)||(e=p(e).closest(O)),k._jQueryInterface.call(p(e),"toggle")}).on(N.FOCUS_BLUR_DATA_API,S,function(t){var e=p(t.target).closest(O)[0];p(e).toggleClass(T,/^focus(in)?$/.test(t.type))}),p.fn[_]=k._jQueryInterface,p.fn[_].Constructor=k,p.fn[_].noConflict=function(){return p.fn[_]=b,k._jQueryInterface};var L="carousel",x="bs.carousel",P="."+x,H=".data-api",j=p.fn[L],R={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},F={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},M="next",W="prev",U="left",B="right",q={SLIDE:"slide"+P,SLID:"slid"+P,KEYDOWN:"keydown"+P,MOUSEENTER:"mouseenter"+P,MOUSELEAVE:"mouseleave"+P,TOUCHSTART:"touchstart"+P,TOUCHMOVE:"touchmove"+P,TOUCHEND:"touchend"+P,POINTERDOWN:"pointerdown"+P,POINTERUP:"pointerup"+P,DRAG_START:"dragstart"+P,LOAD_DATA_API:"load"+P+H,CLICK_DATA_API:"click"+P+H},K="carousel",Q="active",V="slide",Y="carousel-item-right",z="carousel-item-left",X="carousel-item-next",G="carousel-item-prev",$="pointer-event",J=".active",Z=".active.carousel-item",tt=".carousel-item",et=".carousel-item img",nt=".carousel-item-next, .carousel-item-prev",it=".carousel-indicators",ot="[data-slide], [data-slide-to]",rt='[data-ride="carousel"]',st={TOUCH:"touch",PEN:"pen"},at=function(){function r(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=this._element.querySelector(it),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var t=r.prototype;return t.next=function(){this._isSliding||this._slide(M)},t.nextWhenVisible=function(){!document.hidden&&p(this._element).is(":visible")&&"hidden"!==p(this._element).css("visibility")&&this.next()},t.prev=function(){this._isSliding||this._slide(W)},t.pause=function(t){t||(this._isPaused=!0),this._element.querySelector(nt)&&(m.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},t.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},t.to=function(t){var e=this;this._activeElement=this._element.querySelector(Z);var n=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)p(this._element).one(q.SLID,function(){return e.to(t)});else{if(n===t)return this.pause(),void this.cycle();var i=n<t?M:W;this._slide(i,this._items[t])}},t.dispose=function(){p(this._element).off(P),p.removeData(this._element,x),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},t._getConfig=function(t){return t=l({},R,t),m.typeCheckConfig(L,t,F),t},t._handleSwipe=function(){var t,e=Math.abs(this.touchDeltaX);e<=40||(0<(t=e/this.touchDeltaX)&&this.prev(),t<0&&this.next())},t._addEventListeners=function(){var e=this;this._config.keyboard&&p(this._element).on(q.KEYDOWN,function(t){return e._keydown(t)}),"hover"===this._config.pause&&p(this._element).on(q.MOUSEENTER,function(t){return e.pause(t)}).on(q.MOUSELEAVE,function(t){return e.cycle(t)}),this._config.touch&&this._addTouchEventListeners()},t._addTouchEventListeners=function(){var t,e,n=this;this._touchSupported&&(t=function(t){n._pointerEvent&&st[t.originalEvent.pointerType.toUpperCase()]?n.touchStartX=t.originalEvent.clientX:n._pointerEvent||(n.touchStartX=t.originalEvent.touches[0].clientX)},e=function(t){n._pointerEvent&&st[t.originalEvent.pointerType.toUpperCase()]&&(n.touchDeltaX=t.originalEvent.clientX-n.touchStartX),n._handleSwipe(),"hover"===n._config.pause&&(n.pause(),n.touchTimeout&&clearTimeout(n.touchTimeout),n.touchTimeout=setTimeout(function(t){return n.cycle(t)},500+n._config.interval))},p(this._element.querySelectorAll(et)).on(q.DRAG_START,function(t){return t.preventDefault()}),this._pointerEvent?(p(this._element).on(q.POINTERDOWN,t),p(this._element).on(q.POINTERUP,e),this._element.classList.add($)):(p(this._element).on(q.TOUCHSTART,t),p(this._element).on(q.TOUCHMOVE,function(t){var e;(e=t).originalEvent.touches&&1<e.originalEvent.touches.length?n.touchDeltaX=0:n.touchDeltaX=e.originalEvent.touches[0].clientX-n.touchStartX}),p(this._element).on(q.TOUCHEND,e)))},t._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},t._getItemIndex=function(t){return this._items=t&&t.parentNode?[].slice.call(t.parentNode.querySelectorAll(tt)):[],this._items.indexOf(t)},t._getItemByDirection=function(t,e){var n=t===M,i=t===W,o=this._getItemIndex(e),r=this._items.length-1;if((i&&0===o||n&&o===r)&&!this._config.wrap)return e;var s=(o+(t===W?-1:1))%this._items.length;return-1==s?this._items[this._items.length-1]:this._items[s]},t._triggerSlideEvent=function(t,e){var n=this._getItemIndex(t),i=this._getItemIndex(this._element.querySelector(Z)),o=p.Event(q.SLIDE,{relatedTarget:t,direction:e,from:i,to:n});return p(this._element).trigger(o),o},t._setActiveIndicatorElement=function(t){var e,n;this._indicatorsElement&&(e=[].slice.call(this._indicatorsElement.querySelectorAll(J)),p(e).removeClass(Q),(n=this._indicatorsElement.children[this._getItemIndex(t)])&&p(n).addClass(Q))},t._slide=function(t,e){var n,i,o,r,s,a=this,l=this._element.querySelector(Z),c=this._getItemIndex(l),h=e||l&&this._getItemByDirection(t,l),u=this._getItemIndex(h),f=Boolean(this._interval),d=t===M?(n=z,i=X,U):(n=Y,i=G,B);h&&p(h).hasClass(Q)?this._isSliding=!1:this._triggerSlideEvent(h,d).isDefaultPrevented()||l&&h&&(this._isSliding=!0,f&&this.pause(),this._setActiveIndicatorElement(h),o=p.Event(q.SLID,{relatedTarget:h,direction:d,from:c,to:u}),p(this._element).hasClass(V)?(p(h).addClass(i),m.reflow(h),p(l).addClass(n),p(h).addClass(n),(r=parseInt(h.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=r):this._config.interval=this._config.defaultInterval||this._config.interval,s=m.getTransitionDurationFromElement(l),p(l).one(m.TRANSITION_END,function(){p(h).removeClass(n+" "+i).addClass(Q),p(l).removeClass(Q+" "+i+" "+n),a._isSliding=!1,setTimeout(function(){return p(a._element).trigger(o)},0)}).emulateTransitionEnd(s)):(p(l).removeClass(Q),p(h).addClass(Q),this._isSliding=!1,p(this._element).trigger(o)),f&&this.cycle())},r._jQueryInterface=function(i){return this.each(function(){var t=p(this).data(x),e=l({},R,p(this).data());"object"===_typeof(i)&&(e=l({},e,i));var n="string"==typeof i?i:e.slide;if(t||(t=new r(this,e),p(this).data(x,t)),"number"==typeof i)t.to(i);else if("string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}else e.interval&&e.ride&&(t.pause(),t.cycle())})},r._dataApiClickHandler=function(t){var e,n,i,o=m.getSelectorFromElement(this);!o||(e=p(o)[0])&&p(e).hasClass(K)&&(n=l({},p(e).data(),p(this).data()),(i=this.getAttribute("data-slide-to"))&&(n.interval=!1),r._jQueryInterface.call(p(e),n),i&&p(e).data(x).to(i),t.preventDefault())},s(r,null,[{key:"VERSION",get:function(){return"4.3.1"}},{key:"Default",get:function(){return R}}]),r}();p(document).on(q.CLICK_DATA_API,ot,at._dataApiClickHandler),p(window).on(q.LOAD_DATA_API,function(){for(var t=[].slice.call(document.querySelectorAll(rt)),e=0,n=t.length;e<n;e++){var i=p(t[e]);at._jQueryInterface.call(i,i.data())}}),p.fn[L]=at._jQueryInterface,p.fn[L].Constructor=at,p.fn[L].noConflict=function(){return p.fn[L]=j,at._jQueryInterface};var lt="collapse",ct="bs.collapse",ht="."+ct,ut=p.fn[lt],ft={toggle:!0,parent:""},dt={toggle:"boolean",parent:"(string|element)"},pt={SHOW:"show"+ht,SHOWN:"shown"+ht,HIDE:"hide"+ht,HIDDEN:"hidden"+ht,CLICK_DATA_API:"click"+ht+".data-api"},mt="show",gt="collapse",_t="collapsing",vt="collapsed",yt="width",Et="height",bt=".show, .collapsing",wt='[data-toggle="collapse"]',Ct=function(){function a(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var n=[].slice.call(document.querySelectorAll(wt)),i=0,o=n.length;i<o;i++){var r=n[i],s=m.getSelectorFromElement(r),a=[].slice.call(document.querySelectorAll(s)).filter(function(t){return t===e});null!==s&&0<a.length&&(this._selector=s,this._triggerArray.push(r))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var t=a.prototype;return t.toggle=function(){p(this._element).hasClass(mt)?this.hide():this.show()},t.show=function(){var t,e,n,i,o,r,s=this;this._isTransitioning||p(this._element).hasClass(mt)||(this._parent&&0===(t=[].slice.call(this._parent.querySelectorAll(bt)).filter(function(t){return"string"==typeof s._config.parent?t.getAttribute("data-parent")===s._config.parent:t.classList.contains(gt)})).length&&(t=null),t&&(e=p(t).not(this._selector).data(ct))&&e._isTransitioning||(n=p.Event(pt.SHOW),p(this._element).trigger(n),n.isDefaultPrevented()||(t&&(a._jQueryInterface.call(p(t).not(this._selector),"hide"),e||p(t).data(ct,null)),i=this._getDimension(),p(this._element).removeClass(gt).addClass(_t),this._element.style[i]=0,this._triggerArray.length&&p(this._triggerArray).removeClass(vt).attr("aria-expanded",!0),this.setTransitioning(!0),o="scroll"+(i[0].toUpperCase()+i.slice(1)),r=m.getTransitionDurationFromElement(this._element),p(this._element).one(m.TRANSITION_END,function(){p(s._element).removeClass(_t).addClass(gt).addClass(mt),s._element.style[i]="",s.setTransitioning(!1),p(s._element).trigger(pt.SHOWN)}).emulateTransitionEnd(r),this._element.style[i]=this._element[o]+"px")))},t.hide=function(){var t=this;if(!this._isTransitioning&&p(this._element).hasClass(mt)){var e=p.Event(pt.HIDE);if(p(this._element).trigger(e),!e.isDefaultPrevented()){var n=this._getDimension();this._element.style[n]=this._element.getBoundingClientRect()[n]+"px",m.reflow(this._element),p(this._element).addClass(_t).removeClass(gt).removeClass(mt);var i=this._triggerArray.length;if(0<i)for(var o=0;o<i;o++){var r=this._triggerArray[o],s=m.getSelectorFromElement(r);null!==s&&(p([].slice.call(document.querySelectorAll(s))).hasClass(mt)||p(r).addClass(vt).attr("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[n]="";var a=m.getTransitionDurationFromElement(this._element);p(this._element).one(m.TRANSITION_END,function(){t.setTransitioning(!1),p(t._element).removeClass(_t).addClass(gt).trigger(pt.HIDDEN)}).emulateTransitionEnd(a)}}},t.setTransitioning=function(t){this._isTransitioning=t},t.dispose=function(){p.removeData(this._element,ct),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},t._getConfig=function(t){return(t=l({},ft,t)).toggle=Boolean(t.toggle),m.typeCheckConfig(lt,t,dt),t},t._getDimension=function(){return p(this._element).hasClass(yt)?yt:Et},t._getParent=function(){var t,n=this;m.isElement(this._config.parent)?(t=this._config.parent,void 0!==this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);var e='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',i=[].slice.call(t.querySelectorAll(e));return p(i).each(function(t,e){n._addAriaAndCollapsedClass(a._getTargetFromElement(e),[e])}),t},t._addAriaAndCollapsedClass=function(t,e){var n=p(t).hasClass(mt);e.length&&p(e).toggleClass(vt,!n).attr("aria-expanded",n)},a._getTargetFromElement=function(t){var e=m.getSelectorFromElement(t);return e?document.querySelector(e):null},a._jQueryInterface=function(i){return this.each(function(){var t=p(this),e=t.data(ct),n=l({},ft,t.data(),"object"===_typeof(i)&&i?i:{});if(!e&&n.toggle&&/show|hide/.test(i)&&(n.toggle=!1),e||(e=new a(this,n),t.data(ct,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},s(a,null,[{key:"VERSION",get:function(){return"4.3.1"}},{key:"Default",get:function(){return ft}}]),a}();p(document).on(pt.CLICK_DATA_API,wt,function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var n=p(this),e=m.getSelectorFromElement(this),i=[].slice.call(document.querySelectorAll(e));p(i).each(function(){var t=p(this),e=t.data(ct)?"toggle":n.data();Ct._jQueryInterface.call(t,e)})}),p.fn[lt]=Ct._jQueryInterface,p.fn[lt].Constructor=Ct,p.fn[lt].noConflict=function(){return p.fn[lt]=ut,Ct._jQueryInterface};for(var Tt="undefined"!=typeof window&&"undefined"!=typeof document,St=["Edge","Trident","Firefox"],Dt=0,It=0;It<St.length;It+=1)if(Tt&&0<=navigator.userAgent.indexOf(St[It])){Dt=1;break}var At=Tt&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then(function(){e=!1,t()}))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout(function(){e=!1,t()},Dt))}};function Ot(t){return t&&"[object Function]"==={}.toString.call(t)}function Nt(t,e){if(1!==t.nodeType)return[];var n=t.ownerDocument.defaultView.getComputedStyle(t,null);return e?n[e]:n}function kt(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function Lt(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var e=Nt(t),n=e.overflow,i=e.overflowX,o=e.overflowY;return/(auto|scroll|overlay)/.test(n+o+i)?t:Lt(kt(t))}var xt=Tt&&!(!window.MSInputMethodContext||!document.documentMode),Pt=Tt&&/MSIE 10/.test(navigator.userAgent);function Ht(t){return 11===t?xt:10!==t&&xt||Pt}function jt(t){if(!t)return document.documentElement;for(var e=Ht(10)?document.body:null,n=t.offsetParent||null;n===e&&t.nextElementSibling;)n=(t=t.nextElementSibling).offsetParent;var i=n&&n.nodeName;return i&&"BODY"!==i&&"HTML"!==i?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===Nt(n,"position")?jt(n):n:t?t.ownerDocument.documentElement:document.documentElement}function Rt(t){return null!==t.parentNode?Rt(t.parentNode):t}function Ft(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var n=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?t:e,o=n?e:t,r=document.createRange();r.setStart(i,0),r.setEnd(o,0);var s,a,l=r.commonAncestorContainer;if(t!==l&&e!==l||i.contains(o))return"BODY"===(a=(s=l).nodeName)||"HTML"!==a&&jt(s.firstElementChild)!==s?jt(l):l;var c=Rt(t);return c.host?Ft(c.host,e):Ft(t,Rt(e).host)}function Mt(t,e){var n="top"===(1<arguments.length&&void 0!==e?e:"top")?"scrollTop":"scrollLeft",i=t.nodeName;if("BODY"!==i&&"HTML"!==i)return t[n];var o=t.ownerDocument.documentElement;return(t.ownerDocument.scrollingElement||o)[n]}function Wt(t,e){var n="x"===e?"Left":"Top",i="Left"==n?"Right":"Bottom";return parseFloat(t["border"+n+"Width"],10)+parseFloat(t["border"+i+"Width"],10)}function Ut(t,e,n,i){return Math.max(e["offset"+t],e["scroll"+t],n["client"+t],n["offset"+t],n["scroll"+t],Ht(10)?parseInt(n["offset"+t])+parseInt(i["margin"+("Height"===t?"Top":"Left")])+parseInt(i["margin"+("Height"===t?"Bottom":"Right")]):0)}function Bt(t){var e=t.body,n=t.documentElement,i=Ht(10)&&getComputedStyle(n);return{height:Ut("Height",e,n,i),width:Ut("Width",e,n,i)}}var qt=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},Kt=function(t,e,n){return e&&Qt(t.prototype,e),n&&Qt(t,n),t};function Qt(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function Vt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Yt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t};function zt(t){return Yt({},t,{right:t.left+t.width,bottom:t.top+t.height})}function Xt(t){var e,n,i={};try{Ht(10)?(i=t.getBoundingClientRect(),e=Mt(t,"top"),n=Mt(t,"left"),i.top+=e,i.left+=n,i.bottom+=e,i.right+=n):i=t.getBoundingClientRect()}catch(t){}var o,r={left:i.left,top:i.top,width:i.right-i.left,height:i.bottom-i.top},s="HTML"===t.nodeName?Bt(t.ownerDocument):{},a=s.width||t.clientWidth||r.right-r.left,l=s.height||t.clientHeight||r.bottom-r.top,c=t.offsetWidth-a,h=t.offsetHeight-l;return(c||h)&&(c-=Wt(o=Nt(t),"x"),h-=Wt(o,"y"),r.width-=c,r.height-=h),zt(r)}function Gt(t,e,n){var i=2<arguments.length&&void 0!==n&&n,o=Ht(10),r="HTML"===e.nodeName,s=Xt(t),a=Xt(e),l=Lt(t),c=Nt(e),h=parseFloat(c.borderTopWidth,10),u=parseFloat(c.borderLeftWidth,10);i&&r&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var f,d,p=zt({top:s.top-a.top-h,left:s.left-a.left-u,width:s.width,height:s.height});return p.marginTop=0,p.marginLeft=0,!o&&r&&(f=parseFloat(c.marginTop,10),d=parseFloat(c.marginLeft,10),p.top-=h-f,p.bottom-=h-f,p.left-=u-d,p.right-=u-d,p.marginTop=f,p.marginLeft=d),(o&&!i?e.contains(l):e===l&&"BODY"!==l.nodeName)&&(p=function(t,e,n){var i=2<arguments.length&&void 0!==n&&n,o=Mt(e,"top"),r=Mt(e,"left"),s=i?-1:1;return t.top+=o*s,t.bottom+=o*s,t.left+=r*s,t.right+=r*s,t}(p,e)),p}function $t(t){if(!t||!t.parentElement||Ht())return document.documentElement;for(var e=t.parentElement;e&&"none"===Nt(e,"transform");)e=e.parentElement;return e||document.documentElement}function Jt(t,e,n,i,o){var r,s,a,l,c,h=4<arguments.length&&void 0!==o&&o,u={top:0,left:0},f=h?$t(t):Ft(t,e);"viewport"===i?u=function(t,e){var n=1<arguments.length&&void 0!==e&&e,i=t.ownerDocument.documentElement,o=Gt(t,i),r=Math.max(i.clientWidth,window.innerWidth||0),s=Math.max(i.clientHeight,window.innerHeight||0),a=n?0:Mt(i),l=n?0:Mt(i,"left");return zt({top:a-o.top+o.marginTop,left:l-o.left+o.marginLeft,width:r,height:s})}(f,h):(r=void 0,"scrollParent"===i?"BODY"===(r=Lt(kt(e))).nodeName&&(r=t.ownerDocument.documentElement):r="window"===i?t.ownerDocument.documentElement:i,s=Gt(r,f,h),"HTML"!==r.nodeName||function t(e){var n=e.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===Nt(e,"position"))return!0;var i=kt(e);return!!i&&t(i)}(f)?u=s:(l=(a=Bt(t.ownerDocument)).height,c=a.width,u.top+=s.top-s.marginTop,u.bottom=l+s.top,u.left+=s.left-s.marginLeft,u.right=c+s.left));var d="number"==typeof(n=n||0);return u.left+=d?n:n.left||0,u.top+=d?n:n.top||0,u.right-=d?n:n.right||0,u.bottom-=d?n:n.bottom||0,u}function Zt(t,e,i,n,o,r){var s=5<arguments.length&&void 0!==r?r:0;if(-1===t.indexOf("auto"))return t;var a=Jt(i,n,s,o),l={top:{width:a.width,height:e.top-a.top},right:{width:a.right-e.right,height:a.height},bottom:{width:a.width,height:a.bottom-e.bottom},left:{width:e.left-a.left,height:a.height}},c=Object.keys(l).map(function(t){return Yt({key:t},l[t],{area:(e=l[t]).width*e.height});var e}).sort(function(t,e){return e.area-t.area}),h=c.filter(function(t){var e=t.width,n=t.height;return e>=i.clientWidth&&n>=i.clientHeight}),u=0<h.length?h[0].key:c[0].key,f=t.split("-")[1];return u+(f?"-"+f:"")}function te(t,e,n,i){var o=3<arguments.length&&void 0!==i?i:null;return Gt(n,o?$t(e):Ft(e,n),o)}function ee(t){var e=t.ownerDocument.defaultView.getComputedStyle(t),n=parseFloat(e.marginTop||0)+parseFloat(e.marginBottom||0),i=parseFloat(e.marginLeft||0)+parseFloat(e.marginRight||0);return{width:t.offsetWidth+i,height:t.offsetHeight+n}}function ne(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,function(t){return e[t]})}function ie(t,e,n){n=n.split("-")[0];var i=ee(t),o={width:i.width,height:i.height},r=-1!==["right","left"].indexOf(n),s=r?"top":"left",a=r?"left":"top",l=r?"height":"width",c=r?"width":"height";return o[s]=e[s]+e[l]/2-i[l]/2,o[a]=n===a?e[a]-i[c]:e[ne(a)],o}function oe(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function re(t,n,e){return(void 0===e?t:t.slice(0,function(t,e,n){if(Array.prototype.findIndex)return t.findIndex(function(t){return t[e]===n});var i=oe(t,function(t){return t[e]===n});return t.indexOf(i)}(t,"name",e))).forEach(function(t){t.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var e=t.function||t.fn;t.enabled&&Ot(e)&&(n.offsets.popper=zt(n.offsets.popper),n.offsets.reference=zt(n.offsets.reference),n=e(n,t))}),n}function se(t,n){return t.some(function(t){var e=t.name;return t.enabled&&e===n})}function ae(t){for(var e=[!1,"ms","Webkit","Moz","O"],n=t.charAt(0).toUpperCase()+t.slice(1),i=0;i<e.length;i++){var o=e[i],r=o?""+o+n:t;if(void 0!==document.body.style[r])return r}return null}function le(t){var e=t.ownerDocument;return e?e.defaultView:window}function ce(t,e,n,i){n.updateBound=i,le(t).addEventListener("resize",n.updateBound,{passive:!0});var o=Lt(t);return function t(e,n,i,o){var r="BODY"===e.nodeName,s=r?e.ownerDocument.defaultView:e;s.addEventListener(n,i,{passive:!0}),r||t(Lt(s.parentNode),n,i,o),o.push(s)}(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function he(){var t,e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(t=this.reference,e=this.state,le(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach(function(t){t.removeEventListener("scroll",e.updateBound)}),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function ue(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function fe(n,i){Object.keys(i).forEach(function(t){var e="";-1!==["width","height","top","right","bottom","left"].indexOf(t)&&ue(i[t])&&(e="px"),n.style[t]=i[t]+e})}function de(t,e){function n(t){return t}var i=t.offsets,o=i.popper,r=i.reference,s=Math.round,a=Math.floor,l=s(r.width),c=s(o.width),h=-1!==["left","right"].indexOf(t.placement),u=-1!==t.placement.indexOf("-"),f=e?h||u||l%2==c%2?s:a:n,d=e?s:n;return{left:f(l%2==1&&c%2==1&&!u&&e?o.left-1:o.left),top:d(o.top),bottom:d(o.bottom),right:f(o.right)}}var pe=Tt&&/Firefox/i.test(navigator.userAgent);function me(t,e,n){var i,o,r=oe(t,function(t){return t.name===e}),s=!!r&&t.some(function(t){return t.name===n&&t.enabled&&t.order<r.order});return s||(i="`"+e+"`",o="`"+n+"`",console.warn(o+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")),s}var ge=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],_e=ge.slice(3);function ve(t,e){var n=1<arguments.length&&void 0!==e&&e,i=_e.indexOf(t),o=_e.slice(i+1).concat(_e.slice(0,i));return n?o.reverse():o}var ye="flip",Ee="clockwise",be="counterclockwise";function we(t,o,r,e){var s=[0,0],a=-1!==["right","left"].indexOf(e),n=t.split(/(\+|\-)/).map(function(t){return t.trim()}),i=n.indexOf(oe(n,function(t){return-1!==t.search(/,|\s/)}));n[i]&&-1===n[i].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/;return(-1!==i?[n.slice(0,i).concat([n[i].split(l)[0]]),[n[i].split(l)[1]].concat(n.slice(i+1))]:[n]).map(function(t,e){var n=(1===e?!a:a)?"height":"width",i=!1;return t.reduce(function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,i=!0,t):i?(t[t.length-1]+=e,i=!1,t):t.concat(e)},[]).map(function(t){return function(t,e,n,i){var o=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+o[1],s=o[2];if(!r)return t;if(0!==s.indexOf("%"))return"vh"!==s&&"vw"!==s?r:("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*r;var a=void 0;switch(s){case"%p":a=n;break;case"%":case"%r":default:a=i}return zt(a)[e]/100*r}(t,n,o,r)})}).forEach(function(n,i){n.forEach(function(t,e){ue(t)&&(s[i]+=t*("-"===n[e-1]?-1:1))})}),s}var Ce={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e,n,i,o,r,s,a,l=t.placement,c=l.split("-")[0],h=l.split("-")[1];return h&&(n=(e=t.offsets).reference,i=e.popper,s=(o=-1!==["bottom","top"].indexOf(c))?"width":"height",a={start:Vt({},r=o?"left":"top",n[r]),end:Vt({},r,n[r]+n[s]-i[s])},t.offsets.popper=Yt({},i,a[h])),t}},offset:{order:200,enabled:!0,fn:function(t,e){var n=e.offset,i=t.placement,o=t.offsets,r=o.popper,s=o.reference,a=i.split("-")[0],l=void 0,l=ue(+n)?[+n,0]:we(n,r,s,a);return"left"===a?(r.top+=l[0],r.left-=l[1]):"right"===a?(r.top+=l[0],r.left+=l[1]):"top"===a?(r.left+=l[0],r.top-=l[1]):"bottom"===a&&(r.left+=l[0],r.top+=l[1]),t.popper=r,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,i){var e=i.boundariesElement||jt(t.instance.popper);t.instance.reference===e&&(e=jt(e));var n=ae("transform"),o=t.instance.popper.style,r=o.top,s=o.left,a=o[n];o.top="",o.left="",o[n]="";var l=Jt(t.instance.popper,t.instance.reference,i.padding,e,t.positionFixed);o.top=r,o.left=s,o[n]=a,i.boundaries=l;var c=i.priority,h=t.offsets.popper,u={primary:function(t){var e=h[t];return h[t]<l[t]&&!i.escapeWithReference&&(e=Math.max(h[t],l[t])),Vt({},t,e)},secondary:function(t){var e="right"===t?"left":"top",n=h[e];return h[t]>l[t]&&!i.escapeWithReference&&(n=Math.min(h[e],l[t]-("right"===t?h.width:h.height))),Vt({},e,n)}};return c.forEach(function(t){var e=-1!==["left","top"].indexOf(t)?"primary":"secondary";h=Yt({},h,u[e](t))}),t.offsets.popper=h,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,n=e.popper,i=e.reference,o=t.placement.split("-")[0],r=Math.floor,s=-1!==["top","bottom"].indexOf(o),a=s?"right":"bottom",l=s?"left":"top",c=s?"width":"height";return n[a]<r(i[l])&&(t.offsets.popper[l]=r(i[l])-n[c]),n[l]>r(i[a])&&(t.offsets.popper[l]=r(i[a])),t}},arrow:{order:500,enabled:!0,fn:function(t,e){var n;if(!me(t.instance.modifiers,"arrow","keepTogether"))return t;var i=e.element;if("string"==typeof i){if(!(i=t.instance.popper.querySelector(i)))return t}else if(!t.instance.popper.contains(i))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var o=t.placement.split("-")[0],r=t.offsets,s=r.popper,a=r.reference,l=-1!==["left","right"].indexOf(o),c=l?"height":"width",h=l?"Top":"Left",u=h.toLowerCase(),f=l?"left":"top",d=l?"bottom":"right",p=ee(i)[c];a[d]-p<s[u]&&(t.offsets.popper[u]-=s[u]-(a[d]-p)),a[u]+p>s[d]&&(t.offsets.popper[u]+=a[u]+p-s[d]),t.offsets.popper=zt(t.offsets.popper);var m=a[u]+a[c]/2-p/2,g=Nt(t.instance.popper),_=parseFloat(g["margin"+h],10),v=parseFloat(g["border"+h+"Width"],10),y=m-t.offsets.popper[u]-_-v,y=Math.max(Math.min(s[c]-p,y),0);return t.arrowElement=i,t.offsets.arrow=(Vt(n={},u,Math.round(y)),Vt(n,f,""),n),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(p,m){if(se(p.instance.modifiers,"inner"))return p;if(p.flipped&&p.placement===p.originalPlacement)return p;var g=Jt(p.instance.popper,p.instance.reference,m.padding,m.boundariesElement,p.positionFixed),_=p.placement.split("-")[0],v=ne(_),y=p.placement.split("-")[1]||"",E=[];switch(m.behavior){case ye:E=[_,v];break;case Ee:E=ve(_);break;case be:E=ve(_,!0);break;default:E=m.behavior}return E.forEach(function(t,e){if(_!==t||E.length===e+1)return p;_=p.placement.split("-")[0],v=ne(_);var n,i=p.offsets.popper,o=p.offsets.reference,r=Math.floor,s="left"===_&&r(i.right)>r(o.left)||"right"===_&&r(i.left)<r(o.right)||"top"===_&&r(i.bottom)>r(o.top)||"bottom"===_&&r(i.top)<r(o.bottom),a=r(i.left)<r(g.left),l=r(i.right)>r(g.right),c=r(i.top)<r(g.top),h=r(i.bottom)>r(g.bottom),u="left"===_&&a||"right"===_&&l||"top"===_&&c||"bottom"===_&&h,f=-1!==["top","bottom"].indexOf(_),d=!!m.flipVariations&&(f&&"start"===y&&a||f&&"end"===y&&l||!f&&"start"===y&&c||!f&&"end"===y&&h);(s||u||d)&&(p.flipped=!0,(s||u)&&(_=E[e+1]),d&&(y="end"===(n=y)?"start":"start"===n?"end":n),p.placement=_+(y?"-"+y:""),p.offsets.popper=Yt({},p.offsets.popper,ie(p.instance.popper,p.offsets.reference,p.placement)),p=re(p.instance.modifiers,p,"flip"))}),p},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,n=e.split("-")[0],i=t.offsets,o=i.popper,r=i.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return o[s?"left":"top"]=r[n]-(a?o[s?"width":"height"]:0),t.placement=ne(e),t.offsets.popper=zt(o),t}},hide:{order:800,enabled:!0,fn:function(t){if(!me(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,n=oe(t.instance.modifiers,function(t){return"preventOverflow"===t.name}).boundaries;if(e.bottom<n.top||e.left>n.right||e.top>n.bottom||e.right<n.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var n=e.x,i=e.y,o=t.offsets.popper,r=oe(t.instance.modifiers,function(t){return"applyStyle"===t.name}).gpuAcceleration;void 0!==r&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s,a,l=void 0!==r?r:e.gpuAcceleration,c=jt(t.instance.popper),h=Xt(c),u={position:o.position},f=de(t,window.devicePixelRatio<2||!pe),d="bottom"===n?"top":"bottom",p="right"===i?"left":"right",m=ae("transform"),g=void 0,_=void 0,_="bottom"==d?"HTML"===c.nodeName?-c.clientHeight+f.bottom:-h.height+f.bottom:f.top,g="right"==p?"HTML"===c.nodeName?-c.clientWidth+f.right:-h.width+f.right:f.left;l&&m?(u[m]="translate3d("+g+"px, "+_+"px, 0)",u[d]=0,u[p]=0,u.willChange="transform"):(s="bottom"==d?-1:1,a="right"==p?-1:1,u[d]=_*s,u[p]=g*a,u.willChange=d+", "+p);var v={"x-placement":t.placement};return t.attributes=Yt({},v,t.attributes),t.styles=Yt({},u,t.styles),t.arrowStyles=Yt({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){var e,n;return fe(t.instance.popper,t.styles),e=t.instance.popper,n=t.attributes,Object.keys(n).forEach(function(t){!1!==n[t]?e.setAttribute(t,n[t]):e.removeAttribute(t)}),t.arrowElement&&Object.keys(t.arrowStyles).length&&fe(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,n,i,o){var r=te(o,e,t,n.positionFixed),s=Zt(n.placement,r,e,t,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return e.setAttribute("x-placement",s),fe(e,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},Te=(Kt(Se,[{key:"update",value:function(){return function(){var t;this.state.isDestroyed||((t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=te(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=Zt(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=ie(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=re(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,se(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[ae("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=ce(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return he.call(this)}}]),Se);function Se(t,e){var n=this,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};qt(this,Se),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=At(this.update.bind(this)),this.options=Yt({},Se.Defaults,i),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=e&&e.jquery?e[0]:e,this.options.modifiers={},Object.keys(Yt({},Se.Defaults.modifiers,i.modifiers)).forEach(function(t){n.options.modifiers[t]=Yt({},Se.Defaults.modifiers[t]||{},i.modifiers?i.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(t){return Yt({name:t},n.options.modifiers[t])}).sort(function(t,e){return t.order-e.order}),this.modifiers.forEach(function(t){t.enabled&&Ot(t.onLoad)&&t.onLoad(n.reference,n.popper,n.options,t,n.state)}),this.update();var o=this.options.eventsEnabled;o&&this.enableEventListeners(),this.state.eventsEnabled=o}Te.Utils=("undefined"!=typeof window?window:global).PopperUtils,Te.placements=ge,Te.Defaults=Ce;var De="dropdown",Ie="bs.dropdown",Ae="."+Ie,Oe=".data-api",Ne=p.fn[De],ke=new RegExp("38|40|27"),Le={HIDE:"hide"+Ae,HIDDEN:"hidden"+Ae,SHOW:"show"+Ae,SHOWN:"shown"+Ae,CLICK:"click"+Ae,CLICK_DATA_API:"click"+Ae+Oe,KEYDOWN_DATA_API:"keydown"+Ae+Oe,KEYUP_DATA_API:"keyup"+Ae+Oe},xe="disabled",Pe="show",He="dropup",je="dropright",Re="dropleft",Fe="dropdown-menu-right",Me="position-static",We='[data-toggle="dropdown"]',Ue=".dropdown form",Be=".dropdown-menu",qe=".navbar-nav",Ke=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",Qe="top-start",Ve="top-end",Ye="bottom-start",ze="bottom-end",Xe="right-start",Ge="left-start",$e={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic"},Je={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string"},Ze=function(){function c(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var t=c.prototype;return t.toggle=function(){if(!this._element.disabled&&!p(this._element).hasClass(xe)){var t=c._getParentFromElement(this._element),e=p(this._menu).hasClass(Pe);if(c._clearMenus(),!e){var n={relatedTarget:this._element},i=p.Event(Le.SHOW,n);if(p(t).trigger(i),!i.isDefaultPrevented()){if(!this._inNavbar){if(void 0===Te)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var o=this._element;"parent"===this._config.reference?o=t:m.isElement(this._config.reference)&&(o=this._config.reference,void 0!==this._config.reference.jquery&&(o=this._config.reference[0])),"scrollParent"!==this._config.boundary&&p(t).addClass(Me),this._popper=new Te(o,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===p(t).closest(qe).length&&p(document.body).children().on("mouseover",null,p.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),p(this._menu).toggleClass(Pe),p(t).toggleClass(Pe).trigger(p.Event(Le.SHOWN,n))}}}},t.show=function(){var t,e,n;this._element.disabled||p(this._element).hasClass(xe)||p(this._menu).hasClass(Pe)||(t={relatedTarget:this._element},e=p.Event(Le.SHOW,t),n=c._getParentFromElement(this._element),p(n).trigger(e),e.isDefaultPrevented()||(p(this._menu).toggleClass(Pe),p(n).toggleClass(Pe).trigger(p.Event(Le.SHOWN,t))))},t.hide=function(){var t,e,n;this._element.disabled||p(this._element).hasClass(xe)||!p(this._menu).hasClass(Pe)||(t={relatedTarget:this._element},e=p.Event(Le.HIDE,t),n=c._getParentFromElement(this._element),p(n).trigger(e),e.isDefaultPrevented()||(p(this._menu).toggleClass(Pe),p(n).toggleClass(Pe).trigger(p.Event(Le.HIDDEN,t))))},t.dispose=function(){p.removeData(this._element,Ie),p(this._element).off(Ae),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},t.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},t._addEventListeners=function(){var e=this;p(this._element).on(Le.CLICK,function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},t._getConfig=function(t){return t=l({},this.constructor.Default,p(this._element).data(),t),m.typeCheckConfig(De,t,this.constructor.DefaultType),t},t._getMenuElement=function(){var t;return this._menu||(t=c._getParentFromElement(this._element))&&(this._menu=t.querySelector(Be)),this._menu},t._getPlacement=function(){var t=p(this._element.parentNode),e=Ye;return t.hasClass(He)?(e=Qe,p(this._menu).hasClass(Fe)&&(e=Ve)):t.hasClass(je)?e=Xe:t.hasClass(Re)?e=Ge:p(this._menu).hasClass(Fe)&&(e=ze),e},t._detectNavbar=function(){return 0<p(this._element).closest(".navbar").length},t._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=l({},t.offsets,e._config.offset(t.offsets,e._element)||{}),t}:t.offset=this._config.offset,t},t._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),t},c._jQueryInterface=function(n){return this.each(function(){var t=p(this).data(Ie),e="object"===_typeof(n)?n:null;if(t||(t=new c(this,e),p(this).data(Ie,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},c._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var e=[].slice.call(document.querySelectorAll(We)),n=0,i=e.length;n<i;n++){var o,r,s=c._getParentFromElement(e[n]),a=p(e[n]).data(Ie),l={relatedTarget:e[n]};t&&"click"===t.type&&(l.clickEvent=t),a&&(o=a._menu,p(s).hasClass(Pe)&&(t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&p.contains(s,t.target)||(r=p.Event(Le.HIDE,l),p(s).trigger(r),r.isDefaultPrevented()||("ontouchstart"in document.documentElement&&p(document.body).children().off("mouseover",null,p.noop),e[n].setAttribute("aria-expanded","false"),p(o).removeClass(Pe),p(s).removeClass(Pe).trigger(p.Event(Le.HIDDEN,l))))))}},c._getParentFromElement=function(t){var e,n=m.getSelectorFromElement(t);return n&&(e=document.querySelector(n)),e||t.parentNode},c._dataApiKeydownHandler=function(t){if((/input|textarea/i.test(t.target.tagName)?!(32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||p(t.target).closest(Be).length)):ke.test(t.which))&&(t.preventDefault(),t.stopPropagation(),!this.disabled&&!p(this).hasClass(xe))){var e,n=c._getParentFromElement(this),i=p(n).hasClass(Pe);if(!i||i&&(27===t.which||32===t.which))return 27===t.which&&(e=n.querySelector(We),p(e).trigger("focus")),void p(this).trigger("click");var o,r=[].slice.call(n.querySelectorAll(Ke));0!==r.length&&(o=r.indexOf(t.target),38===t.which&&0<o&&o--,40===t.which&&o<r.length-1&&o++,o<0&&(o=0),r[o].focus())}},s(c,null,[{key:"VERSION",get:function(){return"4.3.1"}},{key:"Default",get:function(){return $e}},{key:"DefaultType",get:function(){return Je}}]),c}();p(document).on(Le.KEYDOWN_DATA_API,We,Ze._dataApiKeydownHandler).on(Le.KEYDOWN_DATA_API,Be,Ze._dataApiKeydownHandler).on(Le.CLICK_DATA_API+" "+Le.KEYUP_DATA_API,Ze._clearMenus).on(Le.CLICK_DATA_API,We,function(t){t.preventDefault(),t.stopPropagation(),Ze._jQueryInterface.call(p(this),"toggle")}).on(Le.CLICK_DATA_API,Ue,function(t){t.stopPropagation()}),p.fn[De]=Ze._jQueryInterface,p.fn[De].Constructor=Ze,p.fn[De].noConflict=function(){return p.fn[De]=Ne,Ze._jQueryInterface};var tn="modal",en="bs.modal",nn="."+en,on=p.fn[tn],rn={backdrop:!0,keyboard:!0,focus:!0,show:!0},sn={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},an={HIDE:"hide"+nn,HIDDEN:"hidden"+nn,SHOW:"show"+nn,SHOWN:"shown"+nn,FOCUSIN:"focusin"+nn,RESIZE:"resize"+nn,CLICK_DISMISS:"click.dismiss"+nn,KEYDOWN_DISMISS:"keydown.dismiss"+nn,MOUSEUP_DISMISS:"mouseup.dismiss"+nn,MOUSEDOWN_DISMISS:"mousedown.dismiss"+nn,CLICK_DATA_API:"click"+nn+".data-api"},ln="modal-dialog-scrollable",cn="modal-scrollbar-measure",hn="modal-backdrop",un="modal-open",fn="fade",dn="show",pn=".modal-dialog",mn=".modal-body",gn='[data-toggle="modal"]',_n='[data-dismiss="modal"]',vn=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",yn=".sticky-top",En=function(){function o(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=t.querySelector(pn),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var t=o.prototype;return t.toggle=function(t){return this._isShown?this.hide():this.show(t)},t.show=function(t){var e,n=this;this._isShown||this._isTransitioning||(p(this._element).hasClass(fn)&&(this._isTransitioning=!0),e=p.Event(an.SHOW,{relatedTarget:t}),p(this._element).trigger(e),this._isShown||e.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),p(this._element).on(an.CLICK_DISMISS,_n,function(t){return n.hide(t)}),p(this._dialog).on(an.MOUSEDOWN_DISMISS,function(){p(n._element).one(an.MOUSEUP_DISMISS,function(t){p(t.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(t)})))},t.hide=function(t){var e,n,i,o=this;t&&t.preventDefault(),this._isShown&&!this._isTransitioning&&(e=p.Event(an.HIDE),p(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(n=p(this._element).hasClass(fn))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),p(document).off(an.FOCUSIN),p(this._element).removeClass(dn),p(this._element).off(an.CLICK_DISMISS),p(this._dialog).off(an.MOUSEDOWN_DISMISS),n?(i=m.getTransitionDurationFromElement(this._element),p(this._element).one(m.TRANSITION_END,function(t){return o._hideModal(t)}).emulateTransitionEnd(i)):this._hideModal()))},t.dispose=function(){[window,this._element,this._dialog].forEach(function(t){return p(t).off(nn)}),p(document).off(an.FOCUSIN),p.removeData(this._element,en),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},t.handleUpdate=function(){this._adjustDialog()},t._getConfig=function(t){return t=l({},rn,t),m.typeCheckConfig(tn,t,sn),t},t._showElement=function(t){var e=this,n=p(this._element).hasClass(fn);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),p(this._dialog).hasClass(ln)?this._dialog.querySelector(mn).scrollTop=0:this._element.scrollTop=0,n&&m.reflow(this._element),p(this._element).addClass(dn),this._config.focus&&this._enforceFocus();function i(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,p(e._element).trigger(r)}var o,r=p.Event(an.SHOWN,{relatedTarget:t});n?(o=m.getTransitionDurationFromElement(this._dialog),p(this._dialog).one(m.TRANSITION_END,i).emulateTransitionEnd(o)):i()},t._enforceFocus=function(){var e=this;p(document).off(an.FOCUSIN).on(an.FOCUSIN,function(t){document!==t.target&&e._element!==t.target&&0===p(e._element).has(t.target).length&&e._element.focus()})},t._setEscapeEvent=function(){var e=this;this._isShown&&this._config.keyboard?p(this._element).on(an.KEYDOWN_DISMISS,function(t){27===t.which&&(t.preventDefault(),e.hide())}):this._isShown||p(this._element).off(an.KEYDOWN_DISMISS)},t._setResizeEvent=function(){var e=this;this._isShown?p(window).on(an.RESIZE,function(t){return e.handleUpdate(t)}):p(window).off(an.RESIZE)},t._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop(function(){p(document.body).removeClass(un),t._resetAdjustments(),t._resetScrollbar(),p(t._element).trigger(an.HIDDEN)})},t._removeBackdrop=function(){this._backdrop&&(p(this._backdrop).remove(),this._backdrop=null)},t._showBackdrop=function(t){var e,n,i=this,o=p(this._element).hasClass(fn)?fn:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=hn,o&&this._backdrop.classList.add(o),p(this._backdrop).appendTo(document.body),p(this._element).on(an.CLICK_DISMISS,function(t){i._ignoreBackdropClick?i._ignoreBackdropClick=!1:t.target===t.currentTarget&&("static"===i._config.backdrop?i._element.focus():i.hide())}),o&&m.reflow(this._backdrop),p(this._backdrop).addClass(dn),!t)return;if(!o)return void t();var r=m.getTransitionDurationFromElement(this._backdrop);p(this._backdrop).one(m.TRANSITION_END,t).emulateTransitionEnd(r)}else{!this._isShown&&this._backdrop?(p(this._backdrop).removeClass(dn),e=function(){i._removeBackdrop(),t&&t()},p(this._element).hasClass(fn)?(n=m.getTransitionDurationFromElement(this._backdrop),p(this._backdrop).one(m.TRANSITION_END,e).emulateTransitionEnd(n)):e()):t&&t()}},t._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},t._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},t._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=t.left+t.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},t._setScrollbar=function(){var t,e,n,i,o=this;this._isBodyOverflowing&&(t=[].slice.call(document.querySelectorAll(vn)),e=[].slice.call(document.querySelectorAll(yn)),p(t).each(function(t,e){var n=e.style.paddingRight,i=p(e).css("padding-right");p(e).data("padding-right",n).css("padding-right",parseFloat(i)+o._scrollbarWidth+"px")}),p(e).each(function(t,e){var n=e.style.marginRight,i=p(e).css("margin-right");p(e).data("margin-right",n).css("margin-right",parseFloat(i)-o._scrollbarWidth+"px")}),n=document.body.style.paddingRight,i=p(document.body).css("padding-right"),p(document.body).data("padding-right",n).css("padding-right",parseFloat(i)+this._scrollbarWidth+"px")),p(document.body).addClass(un)},t._resetScrollbar=function(){var t=[].slice.call(document.querySelectorAll(vn));p(t).each(function(t,e){var n=p(e).data("padding-right");p(e).removeData("padding-right"),e.style.paddingRight=n||""});var e=[].slice.call(document.querySelectorAll(""+yn));p(e).each(function(t,e){var n=p(e).data("margin-right");void 0!==n&&p(e).css("margin-right",n).removeData("margin-right")});var n=p(document.body).data("padding-right");p(document.body).removeData("padding-right"),document.body.style.paddingRight=n||""},t._getScrollbarWidth=function(){var t=document.createElement("div");t.className=cn,document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},o._jQueryInterface=function(n,i){return this.each(function(){var t=p(this).data(en),e=l({},rn,p(this).data(),"object"===_typeof(n)&&n?n:{});if(t||(t=new o(this,e),p(this).data(en,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](i)}else e.show&&t.show(i)})},s(o,null,[{key:"VERSION",get:function(){return"4.3.1"}},{key:"Default",get:function(){return rn}}]),o}();p(document).on(an.CLICK_DATA_API,gn,function(t){var e,n=this,i=m.getSelectorFromElement(this);i&&(e=document.querySelector(i));var o=p(e).data(en)?"toggle":l({},p(e).data(),p(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var r=p(e).one(an.SHOW,function(t){t.isDefaultPrevented()||r.one(an.HIDDEN,function(){p(n).is(":visible")&&n.focus()})});En._jQueryInterface.call(p(e),o,this)}),p.fn[tn]=En._jQueryInterface,p.fn[tn].Constructor=En,p.fn[tn].noConflict=function(){return p.fn[tn]=on,En._jQueryInterface};var bn=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],wn={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Cn=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,Tn=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function Sn(t,r,e){if(0===t.length)return t;if(e&&"function"==typeof e)return e(t);for(var n=(new window.DOMParser).parseFromString(t,"text/html"),s=Object.keys(r),a=[].slice.call(n.body.querySelectorAll("*")),i=function(t){var e=a[t],n=e.nodeName.toLowerCase();if(-1===s.indexOf(e.nodeName.toLowerCase()))return e.parentNode.removeChild(e),"continue";var i=[].slice.call(e.attributes),o=[].concat(r["*"]||[],r[n]||[]);i.forEach(function(t){!function(t,e){var n=t.nodeName.toLowerCase();if(-1!==e.indexOf(n))return-1===bn.indexOf(n)||Boolean(t.nodeValue.match(Cn)||t.nodeValue.match(Tn));for(var i=e.filter(function(t){return t instanceof RegExp}),o=0,r=i.length;o<r;o++)if(n.match(i[o]))return 1}(t,o)&&e.removeAttribute(t.nodeName)})},o=0,l=a.length;o<l;o++)i(o);return n.body.innerHTML}var Dn="tooltip",In="bs.tooltip",An="."+In,On=p.fn[Dn],Nn="bs-tooltip",kn=new RegExp("(^|\\s)"+Nn+"\\S+","g"),Ln=["sanitize","whiteList","sanitizeFn"],xn={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object"},Pn={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Hn={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:wn},jn="show",Rn="out",Fn={HIDE:"hide"+An,HIDDEN:"hidden"+An,SHOW:"show"+An,SHOWN:"shown"+An,INSERTED:"inserted"+An,CLICK:"click"+An,FOCUSIN:"focusin"+An,FOCUSOUT:"focusout"+An,MOUSEENTER:"mouseenter"+An,MOUSELEAVE:"mouseleave"+An},Mn="fade",Wn="show",Un=".tooltip-inner",Bn=".arrow",qn="hover",Kn="focus",Qn="click",Vn="manual",Yn=function(){function i(t,e){if(void 0===Te)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}var t=i.prototype;return t.enable=function(){this._isEnabled=!0},t.disable=function(){this._isEnabled=!1},t.toggleEnabled=function(){this._isEnabled=!this._isEnabled},t.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,n=p(t.currentTarget).data(e);n||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),p(t.currentTarget).data(e,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(p(this.getTipElement()).hasClass(Wn))return void this._leave(null,this);this._enter(null,this)}},t.dispose=function(){clearTimeout(this._timeout),p.removeData(this.element,this.constructor.DATA_KEY),p(this.element).off(this.constructor.EVENT_KEY),p(this.element).closest(".modal").off("hide.bs.modal"),this.tip&&p(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,(this._activeTrigger=null)!==this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},t.show=function(){var e=this;if("none"===p(this.element).css("display"))throw new Error("Please use show on visible elements");var t=p.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){p(this.element).trigger(t);var n=m.findShadowRoot(this.element),i=p.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!i)return;var o=this.getTipElement(),r=m.getUID(this.constructor.NAME);o.setAttribute("id",r),this.element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&p(o).addClass(Mn);var s="function"==typeof this.config.placement?this.config.placement.call(this,o,this.element):this.config.placement,a=this._getAttachment(s);this.addAttachmentClass(a);var l=this._getContainer();p(o).data(this.constructor.DATA_KEY,this),p.contains(this.element.ownerDocument.documentElement,this.tip)||p(o).appendTo(l),p(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new Te(this.element,o,{placement:a,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:Bn},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}}),p(o).addClass(Wn),"ontouchstart"in document.documentElement&&p(document.body).children().on("mouseover",null,p.noop);var c,h=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,p(e.element).trigger(e.constructor.Event.SHOWN),t===Rn&&e._leave(null,e)};p(this.tip).hasClass(Mn)?(c=m.getTransitionDurationFromElement(this.tip),p(this.tip).one(m.TRANSITION_END,h).emulateTransitionEnd(c)):h()}},t.hide=function(t){function e(){i._hoverState!==jn&&o.parentNode&&o.parentNode.removeChild(o),i._cleanTipClass(),i.element.removeAttribute("aria-describedby"),p(i.element).trigger(i.constructor.Event.HIDDEN),null!==i._popper&&i._popper.destroy(),t&&t()}var n,i=this,o=this.getTipElement(),r=p.Event(this.constructor.Event.HIDE);p(this.element).trigger(r),r.isDefaultPrevented()||(p(o).removeClass(Wn),"ontouchstart"in document.documentElement&&p(document.body).children().off("mouseover",null,p.noop),this._activeTrigger[Qn]=!1,this._activeTrigger[Kn]=!1,this._activeTrigger[qn]=!1,p(this.tip).hasClass(Mn)?(n=m.getTransitionDurationFromElement(o),p(o).one(m.TRANSITION_END,e).emulateTransitionEnd(n)):e(),this._hoverState="")},t.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},t.isWithContent=function(){return Boolean(this.getTitle())},t.addAttachmentClass=function(t){p(this.getTipElement()).addClass(Nn+"-"+t)},t.getTipElement=function(){return this.tip=this.tip||p(this.config.template)[0],this.tip},t.setContent=function(){var t=this.getTipElement();this.setElementContent(p(t.querySelectorAll(Un)),this.getTitle()),p(t).removeClass(Mn+" "+Wn)},t.setElementContent=function(t,e){"object"!==_typeof(e)||!e.nodeType&&!e.jquery?this.config.html?(this.config.sanitize&&(e=Sn(e,this.config.whiteList,this.config.sanitizeFn)),t.html(e)):t.text(e):this.config.html?p(e).parent().is(t)||t.empty().append(e):t.text(p(e).text())},t.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},t._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=l({},t.offsets,e.config.offset(t.offsets,e.element)||{}),t}:t.offset=this.config.offset,t},t._getContainer=function(){return!1===this.config.container?document.body:m.isElement(this.config.container)?p(this.config.container):p(document).find(this.config.container)},t._getAttachment=function(t){return Pn[t.toUpperCase()]},t._setListeners=function(){var i=this;this.config.trigger.split(" ").forEach(function(t){var e,n;"click"===t?p(i.element).on(i.constructor.Event.CLICK,i.config.selector,function(t){return i.toggle(t)}):t!==Vn&&(e=t===qn?i.constructor.Event.MOUSEENTER:i.constructor.Event.FOCUSIN,n=t===qn?i.constructor.Event.MOUSELEAVE:i.constructor.Event.FOCUSOUT,p(i.element).on(e,i.config.selector,function(t){return i._enter(t)}).on(n,i.config.selector,function(t){return i._leave(t)}))}),p(this.element).closest(".modal").on("hide.bs.modal",function(){i.element&&i.hide()}),this.config.selector?this.config=l({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},t._fixTitle=function(){var t=_typeof(this.element.getAttribute("data-original-title"));!this.element.getAttribute("title")&&"string"===t||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},t._enter=function(t,e){var n=this.constructor.DATA_KEY;(e=e||p(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),p(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusin"===t.type?Kn:qn]=!0),p(e.getTipElement()).hasClass(Wn)||e._hoverState===jn?e._hoverState=jn:(clearTimeout(e._timeout),e._hoverState=jn,e.config.delay&&e.config.delay.show?e._timeout=setTimeout(function(){e._hoverState===jn&&e.show()},e.config.delay.show):e.show())},t._leave=function(t,e){var n=this.constructor.DATA_KEY;(e=e||p(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),p(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusout"===t.type?Kn:qn]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState=Rn,e.config.delay&&e.config.delay.hide?e._timeout=setTimeout(function(){e._hoverState===Rn&&e.hide()},e.config.delay.hide):e.hide())},t._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},t._getConfig=function(t){var e=p(this.element).data();return Object.keys(e).forEach(function(t){-1!==Ln.indexOf(t)&&delete e[t]}),"number"==typeof(t=l({},this.constructor.Default,e,"object"===_typeof(t)&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),m.typeCheckConfig(Dn,t,this.constructor.DefaultType),t.sanitize&&(t.template=Sn(t.template,t.whiteList,t.sanitizeFn)),t},t._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},t._cleanTipClass=function(){var t=p(this.getTipElement()),e=t.attr("class").match(kn);null!==e&&e.length&&t.removeClass(e.join(""))},t._handlePopperPlacementChange=function(t){var e=t.instance;this.tip=e.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},t._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(p(t).removeClass(Mn),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},i._jQueryInterface=function(n){return this.each(function(){var t=p(this).data(In),e="object"===_typeof(n)&&n;if((t||!/dispose|hide/.test(n))&&(t||(t=new i(this,e),p(this).data(In,t)),"string"==typeof n)){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.3.1"}},{key:"Default",get:function(){return Hn}},{key:"NAME",get:function(){return Dn}},{key:"DATA_KEY",get:function(){return In}},{key:"Event",get:function(){return Fn}},{key:"EVENT_KEY",get:function(){return An}},{key:"DefaultType",get:function(){return xn}}]),i}();p.fn[Dn]=Yn._jQueryInterface,p.fn[Dn].Constructor=Yn,p.fn[Dn].noConflict=function(){return p.fn[Dn]=On,Yn._jQueryInterface};var zn="popover",Xn="bs.popover",Gn="."+Xn,$n=p.fn[zn],Jn="bs-popover",Zn=new RegExp("(^|\\s)"+Jn+"\\S+","g"),ti=l({},Yn.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),ei=l({},Yn.DefaultType,{content:"(string|element|function)"}),ni="fade",ii="show",oi=".popover-header",ri=".popover-body",si={HIDE:"hide"+Gn,HIDDEN:"hidden"+Gn,SHOW:"show"+Gn,SHOWN:"shown"+Gn,INSERTED:"inserted"+Gn,CLICK:"click"+Gn,FOCUSIN:"focusin"+Gn,FOCUSOUT:"focusout"+Gn,MOUSEENTER:"mouseenter"+Gn,MOUSELEAVE:"mouseleave"+Gn},ai=function(t){var e,n;function i(){return t.apply(this,arguments)||this}n=t,(e=i).prototype=Object.create(n.prototype),(e.prototype.constructor=e).__proto__=n;var o=i.prototype;return o.isWithContent=function(){return this.getTitle()||this._getContent()},o.addAttachmentClass=function(t){p(this.getTipElement()).addClass(Jn+"-"+t)},o.getTipElement=function(){return this.tip=this.tip||p(this.config.template)[0],this.tip},o.setContent=function(){var t=p(this.getTipElement());this.setElementContent(t.find(oi),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(t.find(ri),e),t.removeClass(ni+" "+ii)},o._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},o._cleanTipClass=function(){var t=p(this.getTipElement()),e=t.attr("class").match(Zn);null!==e&&0<e.length&&t.removeClass(e.join(""))},i._jQueryInterface=function(n){return this.each(function(){var t=p(this).data(Xn),e="object"===_typeof(n)?n:null;if((t||!/dispose|hide/.test(n))&&(t||(t=new i(this,e),p(this).data(Xn,t)),"string"==typeof n)){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.3.1"}},{key:"Default",get:function(){return ti}},{key:"NAME",get:function(){return zn}},{key:"DATA_KEY",get:function(){return Xn}},{key:"Event",get:function(){return si}},{key:"EVENT_KEY",get:function(){return Gn}},{key:"DefaultType",get:function(){return ei}}]),i}(Yn);p.fn[zn]=ai._jQueryInterface,p.fn[zn].Constructor=ai,p.fn[zn].noConflict=function(){return p.fn[zn]=$n,ai._jQueryInterface};var li="scrollspy",ci="bs.scrollspy",hi="."+ci,ui=p.fn[li],fi={offset:10,method:"auto",target:""},di={offset:"number",method:"string",target:"(string|element)"},pi={ACTIVATE:"activate"+hi,SCROLL:"scroll"+hi,LOAD_DATA_API:"load"+hi+".data-api"},mi="dropdown-item",gi="active",_i='[data-spy="scroll"]',vi=".nav, .list-group",yi=".nav-link",Ei=".nav-item",bi=".list-group-item",wi=".dropdown",Ci=".dropdown-item",Ti=".dropdown-toggle",Si="offset",Di="position",Ii=function(){function i(t,e){var n=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" "+yi+","+this._config.target+" "+bi+","+this._config.target+" "+Ci,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,p(this._scrollElement).on(pi.SCROLL,function(t){return n._process(t)}),this.refresh(),this._process()}var t=i.prototype;return t.refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?Si:Di,o="auto"===this._config.method?t:this._config.method,r=o===Di?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(t){var e,n=m.getSelectorFromElement(t);if(n&&(e=document.querySelector(n)),e){var i=e.getBoundingClientRect();if(i.width||i.height)return[p(e)[o]().top+r,n]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},t.dispose=function(){p.removeData(this._element,ci),p(this._scrollElement).off(hi),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},t._getConfig=function(t){var e;return"string"!=typeof(t=l({},fi,"object"===_typeof(t)&&t?t:{})).target&&((e=p(t.target).attr("id"))||(e=m.getUID(li),p(t.target).attr("id",e)),t.target="#"+e),m.typeCheckConfig(li,t,di),t},t._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},t._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},t._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},t._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),n<=t){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var o=this._offsets.length;o--;){this._activeTarget!==this._targets[o]&&t>=this._offsets[o]&&(void 0===this._offsets[o+1]||t<this._offsets[o+1])&&this._activate(this._targets[o])}}},t._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),n=p([].slice.call(document.querySelectorAll(t.join(","))));n.hasClass(mi)?(n.closest(wi).find(Ti).addClass(gi),n.addClass(gi)):(n.addClass(gi),n.parents(vi).prev(yi+", "+bi).addClass(gi),n.parents(vi).prev(Ei).children(yi).addClass(gi)),p(this._scrollElement).trigger(pi.ACTIVATE,{relatedTarget:e})},t._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(t){return t.classList.contains(gi)}).forEach(function(t){return t.classList.remove(gi)})},i._jQueryInterface=function(n){return this.each(function(){var t=p(this).data(ci),e="object"===_typeof(n)&&n;if(t||(t=new i(this,e),p(this).data(ci,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.3.1"}},{key:"Default",get:function(){return fi}}]),i}();p(window).on(pi.LOAD_DATA_API,function(){for(var t=[].slice.call(document.querySelectorAll(_i)),e=t.length;e--;){var n=p(t[e]);Ii._jQueryInterface.call(n,n.data())}}),p.fn[li]=Ii._jQueryInterface,p.fn[li].Constructor=Ii,p.fn[li].noConflict=function(){return p.fn[li]=ui,Ii._jQueryInterface};var Ai="bs.tab",Oi="."+Ai,Ni=p.fn.tab,ki={HIDE:"hide"+Oi,HIDDEN:"hidden"+Oi,SHOW:"show"+Oi,SHOWN:"shown"+Oi,CLICK_DATA_API:"click"+Oi+".data-api"},Li="dropdown-menu",xi="active",Pi="disabled",Hi="fade",ji="show",Ri=".dropdown",Fi=".nav, .list-group",Mi=".active",Wi="> li > .active",Ui='[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',Bi=".dropdown-toggle",qi="> .dropdown-menu .active",Ki=function(){function i(t){this._element=t}var t=i.prototype;return t.show=function(){var t,e,n,i,o,r,s,a,l=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&p(this._element).hasClass(xi)||p(this._element).hasClass(Pi)||(e=p(this._element).closest(Fi)[0],n=m.getSelectorFromElement(this._element),e&&(i="UL"===e.nodeName||"OL"===e.nodeName?Wi:Mi,o=(o=p.makeArray(p(e).find(i)))[o.length-1]),r=p.Event(ki.HIDE,{relatedTarget:this._element}),s=p.Event(ki.SHOW,{relatedTarget:o}),o&&p(o).trigger(r),p(this._element).trigger(s),s.isDefaultPrevented()||r.isDefaultPrevented()||(n&&(t=document.querySelector(n)),this._activate(this._element,e),a=function(){var t=p.Event(ki.HIDDEN,{relatedTarget:l._element}),e=p.Event(ki.SHOWN,{relatedTarget:o});p(o).trigger(t),p(l._element).trigger(e)},t?this._activate(t,t.parentNode,a):a()))},t.dispose=function(){p.removeData(this._element,Ai),this._element=null},t._activate=function(t,e,n){function i(){return r._transitionComplete(t,s,n)}var o,r=this,s=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?p(e).children(Mi):p(e).find(Wi))[0],a=n&&s&&p(s).hasClass(Hi);s&&a?(o=m.getTransitionDurationFromElement(s),p(s).removeClass(ji).one(m.TRANSITION_END,i).emulateTransitionEnd(o)):i()},t._transitionComplete=function(t,e,n){var i,o,r;e&&(p(e).removeClass(xi),(i=p(e.parentNode).find(qi)[0])&&p(i).removeClass(xi),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)),p(t).addClass(xi),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),m.reflow(t),t.classList.contains(Hi)&&t.classList.add(ji),t.parentNode&&p(t.parentNode).hasClass(Li)&&((o=p(t).closest(Ri)[0])&&(r=[].slice.call(o.querySelectorAll(Bi)),p(r).addClass(xi)),t.setAttribute("aria-expanded",!0)),n&&n()},i._jQueryInterface=function(n){return this.each(function(){var t=p(this),e=t.data(Ai);if(e||(e=new i(this),t.data(Ai,e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.3.1"}}]),i}();p(document).on(ki.CLICK_DATA_API,Ui,function(t){t.preventDefault(),Ki._jQueryInterface.call(p(this),"show")}),p.fn.tab=Ki._jQueryInterface,p.fn.tab.Constructor=Ki,p.fn.tab.noConflict=function(){return p.fn.tab=Ni,Ki._jQueryInterface};var Qi="toast",Vi="bs.toast",Yi="."+Vi,zi=p.fn[Qi],Xi={CLICK_DISMISS:"click.dismiss"+Yi,HIDE:"hide"+Yi,HIDDEN:"hidden"+Yi,SHOW:"show"+Yi,SHOWN:"shown"+Yi},Gi="fade",$i="hide",Ji="show",Zi="showing",to={animation:"boolean",autohide:"boolean",delay:"number"},eo={animation:!0,autohide:!0,delay:500},no='[data-dismiss="toast"]',io=function(){function o(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}var t=o.prototype;return t.show=function(){var t=this;p(this._element).trigger(Xi.SHOW),this._config.animation&&this._element.classList.add(Gi);function e(){t._element.classList.remove(Zi),t._element.classList.add(Ji),p(t._element).trigger(Xi.SHOWN),t._config.autohide&&t.hide()}var n;this._element.classList.remove($i),this._element.classList.add(Zi),this._config.animation?(n=m.getTransitionDurationFromElement(this._element),p(this._element).one(m.TRANSITION_END,e).emulateTransitionEnd(n)):e()},t.hide=function(t){var e=this;this._element.classList.contains(Ji)&&(p(this._element).trigger(Xi.HIDE),t?this._close():this._timeout=setTimeout(function(){e._close()},this._config.delay))},t.dispose=function(){clearTimeout(this._timeout),this._timeout=null,this._element.classList.contains(Ji)&&this._element.classList.remove(Ji),p(this._element).off(Xi.CLICK_DISMISS),p.removeData(this._element,Vi),this._element=null,this._config=null},t._getConfig=function(t){return t=l({},eo,p(this._element).data(),"object"===_typeof(t)&&t?t:{}),m.typeCheckConfig(Qi,t,this.constructor.DefaultType),t},t._setListeners=function(){var t=this;p(this._element).on(Xi.CLICK_DISMISS,no,function(){return t.hide(!0)})},t._close=function(){function t(){n._element.classList.add($i),p(n._element).trigger(Xi.HIDDEN)}var e,n=this;this._element.classList.remove(Ji),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),p(this._element).one(m.TRANSITION_END,t).emulateTransitionEnd(e)):t()},o._jQueryInterface=function(i){return this.each(function(){var t=p(this),e=t.data(Vi),n="object"===_typeof(i)&&i;if(e||(e=new o(this,n),t.data(Vi,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](this)}})},s(o,null,[{key:"VERSION",get:function(){return"4.3.1"}},{key:"DefaultType",get:function(){return to}},{key:"Default",get:function(){return eo}}]),o}();p.fn[Qi]=io._jQueryInterface,p.fn[Qi].Constructor=io,p.fn[Qi].noConflict=function(){return p.fn[Qi]=zi,io._jQueryInterface},function(){if(void 0===p)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=p.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||4<=t[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}(),t.Util=m,t.Alert=g,t.Button=k,t.Carousel=at,t.Collapse=Ct,t.Dropdown=Ze,t.Modal=En,t.Popover=ai,t.Scrollspy=Ii,t.Tab=Ki,t.Toast=io,t.Tooltip=Yn,Object.defineProperty(t,"__esModule",{value:!0})});
"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var DateFormat={};!function(){var D=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],I=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],O=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],w=["January","February","March","April","May","June","July","August","September","October","November","December"],a={Jan:"01",Feb:"02",Mar:"03",Apr:"04",May:"05",Jun:"06",Jul:"07",Aug:"08",Sep:"09",Oct:"10",Nov:"11",Dec:"12"},s=/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.?\d{0,3}[Z\-+]?(\d{2}:?\d{2})?/;function i(e){return a[e]||e}function u(e){var a,t,r,n,o,s=e,i="";return-1!==s.indexOf(".")&&(s=(n=s.split("."))[0],i=n[n.length-1]),3<=(o=s.split(":")).length?(a=o[0],t=o[1],r=o[2].replace(/\s.+/,"").replace(/[a-z]/gi,""),{time:s=s.replace(/\s.+/,"").replace(/[a-z]/gi,""),hour:a,minute:t,second:r,millis:i}):{time:"",hour:"",minute:"",second:"",millis:""}}function v(e,a){for(var t=a-String(e).length,r=0;r<t;r++)e="0"+e;return e}DateFormat.format={parseDate:function(e){var a,t,r={date:null,year:null,month:null,dayOfMonth:null,dayOfWeek:null,time:null};if("number"==typeof e)return this.parseDate(new Date(e));if("function"==typeof e.getFullYear)r.year=String(e.getFullYear()),r.month=String(e.getMonth()+1),r.dayOfMonth=String(e.getDate()),r.time=u(e.toTimeString()+"."+e.getMilliseconds());else if(-1!=e.search(s))a=e.split(/[T\+-]/),r.year=a[0],r.month=a[1],r.dayOfMonth=a[2],r.time=u(a[3].split(".")[0]);else switch(6===(a=e.split(" ")).length&&isNaN(a[5])&&(a[a.length]="()"),a.length){case 6:r.year=a[5],r.month=i(a[1]),r.dayOfMonth=a[2],r.time=u(a[3]);break;case 2:t=a[0].split("-"),r.year=t[0],r.month=t[1],r.dayOfMonth=t[2],r.time=u(a[1]);break;case 7:case 9:case 10:r.year=a[3];var n=parseInt(a[1]),o=parseInt(a[2]);n&&!o?(r.month=i(a[2]),r.dayOfMonth=a[1]):(r.month=i(a[1]),r.dayOfMonth=a[2]),r.time=u(a[4]);break;case 1:t=a[0].split(""),r.year=t[0]+t[1]+t[2]+t[3],r.month=t[5]+t[6],r.dayOfMonth=t[8]+t[9],r.time=u(t[13]+t[14]+t[15]+t[16]+t[17]+t[18]+t[19]+t[20]);break;default:return null}return r.time?r.date=new Date(r.year,r.month-1,r.dayOfMonth,r.time.hour,r.time.minute,r.time.second,r.time.millis):r.date=new Date(r.year,r.month-1,r.dayOfMonth),r.dayOfWeek=String(r.date.getDay()),r},date:function(e,a){try{var t=this.parseDate(e);if(null===t)return e;for(var r,n=t.year,o=t.month,s=t.dayOfMonth,i=t.dayOfWeek,u=t.time,c="",y="",l="",m=!1,f=0;f<a.length;f++){var h=a.charAt(f),b=a.charAt(f+1);if(m)"'"==h?(y+=""===c?"'":c,c="",m=!1):c+=h;else switch(l="",c+=h){case"ddd":y+=(S=i,D[parseInt(S,10)]||S),c="";break;case"dd":if("d"===b)break;y+=v(s,2),c="";break;case"d":if("d"===b)break;y+=parseInt(s,10),c="";break;case"D":y+=s=1==s||21==s||31==s?parseInt(s,10)+"st":2==s||22==s?parseInt(s,10)+"nd":3==s||23==s?parseInt(s,10)+"rd":parseInt(s,10)+"th",c="";break;case"MMMM":y+=(M=o,g=parseInt(M,10)-1,w[g]||M),c="";break;case"MMM":if("M"===b)break;y+=(p=o,k=parseInt(p,10)-1,O[k]||p),c="";break;case"MM":if("M"===b)break;y+=v(o,2),c="";break;case"M":if("M"===b)break;y+=parseInt(o,10),c="";break;case"y":case"yyy":if("y"===b)break;y+=c,c="";break;case"yy":if("y"===b)break;y+=String(n).slice(-2),c="";break;case"yyyy":y+=n,c="";break;case"HH":y+=v(u.hour,2),c="";break;case"H":if("H"===b)break;y+=parseInt(u.hour,10),c="";break;case"hh":y+=v(r=0===parseInt(u.hour,10)?12:u.hour<13?u.hour:u.hour-12,2),c="";break;case"h":if("h"===b)break;r=0===parseInt(u.hour,10)?12:u.hour<13?u.hour:u.hour-12,y+=parseInt(r,10),c="";break;case"mm":y+=v(u.minute,2),c="";break;case"m":if("m"===b)break;y+=parseInt(u.minute,10),c="";break;case"ss":y+=v(u.second.substring(0,2),2),c="";break;case"s":if("s"===b)break;y+=parseInt(u.second,10),c="";break;case"S":case"SS":if("S"===b)break;y+=c,c="";break;case"SSS":y+=v(u.millis.substring(0,3),3),c="";break;case"a":y+=12<=u.hour?"PM":"AM",c="";break;case"p":y+=12<=u.hour?"p.m.":"a.m.",c="";break;case"E":y+=(d=i,I[parseInt(d,10)]||d),c="";break;case"'":m=!(c="");break;default:y+=h,c=""}}return y+l}catch(a){return console&&console.log&&console.log(a),e}var d,p,k,M,g,S},prettyDate:function(e){var a,t,r,n,o;if("string"!=typeof e&&"number"!=typeof e||(a=new Date(e)),"object"==_typeof(e)&&(a=new Date(e.toString())),t=((new Date).getTime()-a.getTime())/1e3,r=Math.abs(t),n=Math.floor(r/86400),!isNaN(n))return o=t<0?"from now":"ago",r<60?0<=t?"just now":"in a moment":r<120?"1 minute "+o:r<3600?Math.floor(r/60)+" minutes "+o:r<7200?"1 hour "+o:r<86400?Math.floor(r/3600)+" hours "+o:1===n?0<=t?"Yesterday":"Tomorrow":n<7?n+" days "+o:7===n?"1 week "+o:n<31?Math.ceil(n/7)+" weeks "+o:"more than 5 weeks "+o},toBrowserTimeZone:function(e,a){return this.date(new Date(e),a||"MM/dd/yyyy HH:mm:ss")}}}(),jQuery.format=DateFormat.format;
