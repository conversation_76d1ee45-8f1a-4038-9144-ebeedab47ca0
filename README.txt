=== easy.jobs- Best Recruitment Plugin for Job Board Listing, Manager, Career Page for Elementor & Gutenberg ===
Contributors: wpdevteam, re_enter_rupok, asif2bd, rahat89, priyomukul, nhrrob, fuadragib
Tags: Job Listing, Job Board, Job Portal, Job Manager, Career Page,
Requires at least: 5.0
Requires PHP: 5.6
Tested up to: 6.8
Stable tag: 2.6.1
License: GPLv3 or later
License URI: https://opensource.org/licenses/GPL-3.0


Easy solution for job recruitment to attract, manage & hire the right talent faster. Best Job Manager in WordPress with Elementor support.


== Description ==

**EasyJobs - Best & Easiest Job Manager Plugin for WordPress**

[easy.jobs](https://easy.jobs) is a SAAS recruitment solution which comes with full functionality from  WordPress dashboard and it also has Elementor compatibility to manage the hiring process without using any codes. You can get started for FREE & use easy.jobs powerful integration with WordPress.

Need a job board or complete hiring solution to take your company to the next level? Recruiting the right team member is the most important but quite difficult task for any business.

However, you can make it easier & effective using the right solution that lets you track, analyze, communicate & most importantly evaluate candidates through advanced filtering systems from a single platform.

**Introducing EasyJobs - Easiest solution for the job recruitment to attract, manage & hire right talent faster.**

https://www.youtube.com/watch?v=xp1E65oLnlc

## Why Does Your Company Need easy.jobs? ##

🔸 Attract & Convert The Right Talent
🔸 Faster & Better Hiring Process
🔸 Structured Candidate Pipeline
🔸 Ready Career Site WordPress Templates
🔸 Visual Pipeline For Screening Candidates
🔸 Customize Email Notifications
🔸 In-built Analytics To Analyze Data (Pro)
🔸 Export Candidate Data (Pro)
🔸 AI-Powered Screening System (Pro)
🔸 Manage Team Activity (Pro)
🔸 Create Career Site With Custom Domain (Pro)
🔸 Help Job Posts To Get Indexed On Google (Pro)
🔸 Powerful Integration With ZOOM & Google Meet (Pro)
🔸 Dedicated Support For WordPress Plugin
🔸 Seamless Integration With Elementor Page builder

## 🔥 KEY FEATURES FOR EXCEPTIONAL HIRING ##

### 💯 Get Your Entire Team Onboard ###

🔸 **Collaborate With Hiring Team:** Provide personalized team access to have full control over the recruiting team and make better & faster hiring decisions.

🔸 **Insightful Reports From Analytics:** Use advanced & automated reports to track activities and monitor the recruiting team to improve the hiring workflow.

🔸 **Instant Smart Notifications:** Get an instant notification to all the updates and also make your applicants alert for future activities to be prepared.

🔸 **AI-Powered Screening System:** Sort, filter, and screen candidates faster with the power of easy.jobs Artificial Intelligence.

🔸 **Set Up Remote Hiring Easily:** Recruit top talent remotely with easy.jobs remote hiring features.

https://www.youtube.com/watch?v=cdlfZo3Wlhg


## 🌟 Find The Right Candidate Faster ##

🔸 **Smart Candidate Selection:** Analyze and filter your desired applicants to get the right one & make the recruitment process faster than ever before.

🔸 **Interactive Interview Process:** Quiz, Personality Test & more to make the interview engaging for both and increase the brand value to the candidate.

🔸 **Custom Job Apply Fields, Skills & Categories:** Filter applicants more thoroughly by adding custom apply fields, skills, and more.

🔸 **In-App Messaging With Candidates:** Choose, Track and instantly contact with the candidate via messaging & make the hiring process smoother & effective.

## 💼 Establish Your Employer Brand ##

🔸 **Customize Application Forms:** Include job specific questionnaires or just ask some general questions to filter candidate profiles based on their responses

🔸 **Branded Company Profile:** Create stunning company page to showcase employee benefits & existing talented team activities to attract the top talent

🔸 **Personalized Career Pages:** Customizable yet responsive and professional looking career page that brings your company maximum exposure for the job posts

https://www.youtube.com/watch?v=FcuckdbSL9E


## 🌟 Build Your Career Site On WordPress ##

🔸 **Connect easy.jobs With WordPress:** Instantly connect easy.jobs to your WordPress website with API keys.

🔸 **Full Control From WordPress Dashboard:** Manage your entire hiring process effortlessly straight from your WordPress dashboard.

🔸 **Create New Jobs, Edit Pipelines & More:** Add new job posts, manage your hiring pipeline, and more with easy.jobs WordPress plugin.
🔸 **Seamless Integration With Elementor:** Build your career site just the way you want with your favorite [Elementor page builder](https://easy.jobs/blog/wordpress-job-board-elementor/).

https://www.youtube.com/watch?v=YVoD_PZNAQ4


## 🚀 Backed By A Trusted Team ##

This Recruitment solution is brought to you by the team behind [WPDeveloper](https://wpdeveloper.com), a dedicated marketplace for WordPress, trusted by 5,000,000+ happy users.

## 👨‍💻 DOCUMENTATION AND SUPPORT ##

* For documentation and tutorials go to our [Documentation](https://easy.jobs/docs/)
* If you have any more questions, visit our support on the Plugin's Forum
* For more information about features, FAQs and documentation, check out our website at [easy.jobs](https://easy.jobs/)



== Installation ==

1. Upload `easyjobs` to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress

== Frequently Asked Questions ==

= Do I need to sign up to easy.jobs? =

Yes, as it's a Software as a Service (SaaS), you need to sign up to easy.jobs first.

= Is it free to sign up to easy.jobs? =

Yes, as it's free to get start and you can use the free package and upgrade anytime you want.

= Do I need a credit card to sign up? =

No, credit card is not required to sign up.


== Screenshots ==

1. easy.jobs Dashboard View
2. Job Board
3. A candidate profile
4. Question set view
5. Theme setup view
6. Company profile


== Changelog ==

= 2.6.1 - 03/07/2025 =
* Added: Internal job filtering in Jobs.
* Added: Visibility control for Company explore button in settings.
* Added: Time Zone control in settings.
* Few minor bug fixes & improvements.

= 2.6.0 - 20/04/2025 =
* Added: WordPress 6.8 compatibility.
* Improved: API Responses for Synchronizing the Application and Plugin.
* Few minor bug fixes & improvements.

= 2.5.10 - 24/03/2025 =
* Added: An option for users to easily communicate for support.
* Fixed: Showing expired label in Draft and Archived jobs.
* Improved: API response and UI for Question Set and Assessments.
* Few minor bug fixes & improvements.

= 2.5.9 - 02/03/2025 =
* Added: Show on job board option.
* Added: Allow no job experience option.
* Added: All Jobs in Job filtering list.
* Fixed: Domain or sub-domain validation issue.
* Fixed: AI Setup enable/disable issue in settings.
* Few minor bug fixes & improvements.

= 2.5.8 - 14/01/2025 =
* Fixed: Plugin conflict with Essential Blocks.
* Fixed: UI issue inside subscription.
* Few minor bug fixes & improvements.

= 2.5.7 - 29/12/2024 =
* Hotfix: Brand color confirmation notice error.
* Few minor bug fixes & improvements.

= 2.5.6 - 22/12/2024 =
* Improved: Company size UI in Create Company Form & Settings.
* Updated: Data list for Industries in Create Company Form.
* Few minor bug fixes & improvements.

= 2.5.5 - 14/11/2024 =
* Added: WordPress 6.7 compatibility.
* Added: Brand color support in Landing page and Job Details pages.
* Fixed: Page width issue for Job Details shortcode in Twenty Twenty-Four theme.
* Few minor bug fixes & improvements.

= 2.5.4 - 23/10/2024 =
* Fixed: Frontend job filter not working after permalink change.
* Fixed: Multiple API connection messages in the Career Page Block.
* Few minor bug fixes & improvements.

= 2.5.3 - 17/10/2024 =
* Hotfix: Resolved the issue with repeated API calls.
* Few minor bug fixes & improvements.

= 2.5.2 - 16/10/2024 =
* Added : Career Page Block for Gutenberg editor.
* Added : Documentation link for all blocks.
* Few minor bug fixes & improvements.

= 2.5.1 - 02/10/2024 =
* Added : Job Header & Job Footer Block for Gutenberg editor.
* Fixed: Plugin conflict with Essential Blocks.
* Fixed: Image upload issue.
* Improved: Pagination for Job List and Landing Page.
* Few minor bug fixes & improvements.

= 2.5.0 - 19/09/2024 =
* Added : Job List Block for Gutenberg editor.
* Few minor bug fixes & improvements.

= Earlier versions =
Please refer to the separate changelog.txt file.

== Upgrade Notice ==
[Important Update] Performance issue
