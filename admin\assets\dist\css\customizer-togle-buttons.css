.easyjobs-customize-subtitle {
    display: block;
    margin: 0 -12px;
    border: 1px solid #ddd;
    border-left: 0;
    border-right: 0;
    padding: 15px;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 2px;
    line-height: 1;
    text-transform: uppercase;
    color: #555;
    background-color: #fff;
}

.customize-control.customize-control-light {
    margin: 15px 0;
}

input[type=checkbox].tgl {
    display: none;
}
input[type=checkbox].tgl, input[type=checkbox].tgl:after, input[type=checkbox].tgl:before, input[type=checkbox].tgl *, input[type=checkbox].tgl *:after, input[type=checkbox].tgl *:before, input[type=checkbox].tgl + .tgl-btn {
    box-sizing: border-box;
}
input[type=checkbox].tgl::-moz-selection, input[type=checkbox].tgl:after::-moz-selection, input[type=checkbox].tgl:before::-moz-selection, input[type=checkbox].tgl *::-moz-selection, input[type=checkbox].tgl *:after::-moz-selection, input[type=checkbox].tgl *:before::-moz-selection, input[type=checkbox].tgl + .tgl-btn::-moz-selection {
    background: none;
}
input[type=checkbox].tgl::selection, input[type=checkbox].tgl:after::selection, input[type=checkbox].tgl:before::selection, input[type=checkbox].tgl *::selection, input[type=checkbox].tgl *:after::selection, input[type=checkbox].tgl *:before::selection, input[type=checkbox].tgl + .tgl-btn::selection {
  background: none;
}
input[type=checkbox].tgl + .tgl-btn {
    outline: 0;
    display: block;
    width: 4em;
    height: 2em;
    position: relative;
    cursor: pointer;
    -webkit-user-select: none;
      -moz-user-select: none;
        -ms-user-select: none;
            user-select: none;
}
input[type=checkbox].tgl + .tgl-btn:after, input[type=checkbox].tgl + .tgl-btn:before {
    position: relative;
    display: block;
    content: "";
    width: 50%;
    height: 100%;
}
input[type=checkbox].tgl + .tgl-btn:after {
    left: 0;
}
input[type=checkbox].tgl + .tgl-btn:before {
    display: none;
}
input[type=checkbox].tgl:checked + .tgl-btn:after {
    left: 50%;
}

input[type=checkbox].tgl-light + .tgl-btn {
    background: #b5b5b5;
    border-radius: 2em;
    padding: 2px;
    -webkit-transition: all .4s ease;
    transition: all .4s ease;
}
input[type=checkbox].tgl-light + .tgl-btn:after {
    border-radius: 50%;
    background: #fff;
    -webkit-transition: all .2s ease;
    transition: all .2s ease;
}
input[type=checkbox].tgl-light:checked + .tgl-btn {
    background: #597dfc;
}

input[type=checkbox].tgl-ios + .tgl-btn {
    background: #fbfbfb;
    border-radius: 2em;
    padding: 2px;
    -webkit-transition: all .4s ease;
    transition: all .4s ease;
    border: 1px solid #e8eae9;
}
input[type=checkbox].tgl-ios + .tgl-btn:after {
    border-radius: 2em;
    background: #fbfbfb;
    -webkit-transition: left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.3s ease, margin 0.3s ease;
    transition: left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.3s ease, margin 0.3s ease;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 4px 0 rgba(0, 0, 0, 0.08);
}
input[type=checkbox].tgl-ios + .tgl-btn:hover:after {
    will-change: padding;
}
input[type=checkbox].tgl-ios + .tgl-btn:active {
    box-shadow: inset 0 0 0 2em #e8eae9;
}
input[type=checkbox].tgl-ios + .tgl-btn:active:after {
    padding-right: .8em;
}
input[type=checkbox].tgl-ios:checked + .tgl-btn {
    background: #597dfc;
}
input[type=checkbox].tgl-ios:checked + .tgl-btn:active {
    box-shadow: none;
}
input[type=checkbox].tgl-ios:checked + .tgl-btn:active:after {
    margin-left: -.8em;
}

input[type=checkbox].tgl-flat + .tgl-btn {
    padding: 2px;
    -webkit-transition: all .2s ease;
    transition: all .2s ease;
    background: #fff;
    border: 4px solid #f2f2f2;
    border-radius: 2em;
}
input[type=checkbox].tgl-flat + .tgl-btn:after {
    -webkit-transition: all .2s ease;
    transition: all .2s ease;
    background: #f2f2f2;
    content: "";
    border-radius: 1em;
}
input[type=checkbox].tgl-flat:checked + .tgl-btn {
    border: 4px solid #7FC6A6;
}
input[type=checkbox].tgl-flat:checked + .tgl-btn:after {
    left: 50%;
    background: #7FC6A6;
}
