(()=>{"use strict";var e={n:t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return e.d(o,{a:o}),o},d:(t,o)=>{for(var a in o)e.o(o,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:o[a]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{TypoprefixTextJobListCompanyName:()=>re,TypoprefixTextJobListDeadline:()=>me,TypoprefixTextJobListJobApplyBtn:()=>be,TypoprefixTextJobListJobLocation:()=>ie,TypoprefixTextJobListJobTitle:()=>se,TypoprefixTextJobListNoOfJobs:()=>ce,TypoprefixTextJobListTitle:()=>ae,TypoprefixTextResetBtn:()=>le,TypoprefixTextSubmitBtn:()=>ne});var o={};e.r(o),e.d(o,{TypoprefixCompanyName:()=>tt,TypoprefixDescription:()=>nt,TypoprefixLocationName:()=>ot,TypoprefixWebsiteLink:()=>at});var a={};e.r(a),e.d(a,{TypoprefixGalleryTitle:()=>Ft});const n=window.wp.apiFetch;var l=e.n(n);const s=window.wp.url,r=window.wp.data,i={companyInfo:[],companyDetails:[]},c={setCompanyInfo:e=>({type:"SET_COMPANY_INFO",companyInfo:e}),setCompanyDetails:e=>({type:"SET_COMPANY_DETAILS",companyDetails:e}),fetchFromAPI:(e={})=>({type:"FETCH_FROM_API",params:e})},m=(0,r.createReduxStore)("easyjobs",{reducer(e=i,t){switch(t.type){case"SET_COMPANY_INFO":return{...e,companyInfo:t.companyInfo};case"SET_COMPANY_DETAILS":return{...e,companyDetails:t.companyDetails}}return e},actions:c,selectors:{getCompanyInfo:(e,t)=>e.companyInfo,getCompanyDetails:(e,t)=>e.companyDetails},controls:{FETCH_FROM_API(e){let t=EasyJobsBlocksJs.ajax_url;return e.params&&Object.keys(e.params).length&&(t=(0,s.addQueryArgs)(t,e.params)),l()({url:t})}},resolvers:{*getCompanyInfo(e){const t=yield c.fetchFromAPI({action:"easyjobs_get_company_info",blocks_nonce:EasyJobsBlocksJs.blocks_nonce,...e});return"success"===t.status?"success"===t.status?c.setCompanyInfo(t.data):console.log("Unable to fetch data."):"api-error"===t.status?"api-error"===t.status?c.setCompanyInfo(["api-error"]):console.log("Unable to fetch data."):void 0},*getCompanyDetails(e){const t=yield c.fetchFromAPI({action:"easyjobs_get_company_details",blocks_nonce:EasyJobsBlocksJs.blocks_nonce,...e});return"success"===t.status?"success"===t.status?c.setCompanyDetails(t.data):console.log("Unable to fetch data."):"api-error"===t.status?"api-error"===t.status?c.setCompanyDetails(["api-error"]):console.log("Unable to fetch data."):void 0}}});(0,r.register)(m);const b=window.wp.blocks,p=window.React,j=window.wp.blockEditor,y=window.wp.i18n,d=window.wp.components,g=({link:e})=>(0,p.createElement)("div",{className:"eb-panel-control eb-support-panel"},(0,p.createElement)("div",{className:"eb-block-support"},(0,p.createElement)("img",{src:`${EasyJobsLocalize?.image_url}/easyjobs.svg`,alt:"Doc Icon"}),(0,p.createElement)("a",{href:e,target:"_blank"},"Need Help?")),(0,p.createElement)("div",{className:"eb-block-links"},(0,p.createElement)(d.Button,{href:e,target:"_blank"},(0,p.createElement)("img",{src:`${EasyJobsLocalize?.image_url}/doc-icon.svg`,alt:"Doc Icon"}),"Doc"))),u=({attributes:e,setAttributes:t,clientId:o})=>{const{hideJobHeader:a,hideJobList:n,hideJobFooter:l}=e,s=()=>{t({hideJobHeader:!a})},r=()=>{t({hideJobList:!n})},i=()=>{t({hideJobFooter:!l})};return(0,p.createElement)(j.InspectorControls,{key:"controls"},(0,p.createElement)("div",{className:"eb-panel-control"},(0,p.createElement)(d.TabPanel,{className:"eb-parent-tab-panel",activeClass:"active-tab",tabs:[{name:"content",title:(0,y.__)("Content","easyjobs"),className:"eb-tab general"},{name:"styles",title:(0,y.__)("Style","easyjobs"),className:"eb-tab styles"},{name:"advance",title:(0,y.__)("Advanced","easyjobs"),className:"eb-tab advance"}]},(e=>(0,p.createElement)("div",{className:"eb-tab-controls "+e.name},"content"===e.name&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.PanelBody,{title:(0,y.__)("EasyJobs","easyjobs")},(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Hide Job Header","easyjobs"),checked:a,onChange:s}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Hide Job List","easyjobs"),checked:n,onChange:r}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Hide Job Footer","easyjobs"),checked:l,onChange:i}))),"styles"===e.name&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Cobined Block","easyjobs"),initialOpen:!0},(0,p.createElement)(d.Card,null,(0,p.createElement)(d.CardBody,null,(0,p.createElement)("p",null,"Please select the Specific Block for Style Customization."))))))))),(0,p.createElement)(g,{link:"https://easy.jobs/docs/how-to-design-company-career-page-with-gutenberg/"}))},_=window.wp.element,{BlockProps:h}=window.EJControls,f=(0,r.withSelect)(((e,t)=>({companyInfo:e("easyjobs").getCompanyInfo()})))((e=>{const{isSelected:t,attributes:o,companyInfo:a,clientId:n}=e,[l,s]=(0,_.useState)(!1),{hideJobHeader:i,hideJobList:c,hideJobFooter:m,cover:y}=o;(0,_.useEffect)((()=>{1===a?.length&&"api-error"===a[0]&&s(!0)}),[a]);let d=[["easyjobs/job-header",{}],["easyjobs/job-list",{}],["easyjobs/job-footer",{}]];d=d.filter(((e,t)=>0===t&&!i||1===t&&!c||2===t&&!m));const{innerBlocks:g}=(0,r.useSelect)((e=>{const{getBlock:t}=e("core/block-editor");return t(n)||{}}),[n]),f=(e,t)=>e.some((e=>e.name===t));return(0,_.useEffect)((()=>{if(g.length){let e=[...g];[{attr:"hideJobHeader",block:"easyjobs/job-header"}].forEach((t=>{if(o[t.attr])e=e.filter((e=>e.name!==t.block));else if(!f(e,"easyjobs/job-header")){const o=(0,b.createBlock)(t.block,{});e.splice(0,0,o)}})),(0,r.dispatch)("core/block-editor").replaceInnerBlocks(n,e)}}),[i]),(0,_.useEffect)((()=>{if(g.length){let e=[...g],t=1;i&&(t=0),[{attr:"hideJobList",block:"easyjobs/job-list"}].forEach((a=>{if(o[a.attr])e=e.filter((e=>e.name!==a.block));else if(!f(e,"easyjobs/job-list")){const o=(0,b.createBlock)(a.block,{});e.splice(t,0,o)}})),(0,r.dispatch)("core/block-editor").replaceInnerBlocks(n,e)}}),[c]),(0,_.useEffect)((()=>{if(g.length){let e=[...g],t=2;i&&c?t=0:(i||c)&&(t=1),[{attr:"hideJobFooter",block:"easyjobs/job-footer"}].forEach((a=>{if(o[a.attr])e=e.filter((e=>e.name!==a.block));else if(!f(e,"easyjobs/job-footer")){const o=(0,b.createBlock)(a.block,{});e.splice(t,0,o)}})),(0,r.dispatch)("core/block-editor").replaceInnerBlocks(n,e)}}),[m]),y.length?(0,p.createElement)("div",null,(0,p.createElement)("img",{src:y,alt:"landing page",style:{maxWidth:"100%"}})):(0,p.createElement)(p.Fragment,null,t&&!l&&(0,p.createElement)(u,{...e}),(0,p.createElement)(h.Edit,{...e},l?(0,p.createElement)("p",{className:"elej-error-msg-editor"},"Please Connect your EasyJobs Account"):(0,p.createElement)(j.InnerBlocks,{template:d,templateLock:!1,allowedBlocks:["easyjobs/job-header","easyjobs/job-list","easyjobs/job-footer"]})))})),{BlockProps:E}=window.EJControls,C=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"easyjobs/job-landing","icon":"info","version":"0.1.0","title":"Career Page","category":"easyjobs","description":"Customize your career page with a company profile, job list and company gallery for more personalization.","editorStyle":"ej-editor-style","supports":{"anchor":true,"align":["wide","full"]},"textdomain":"easyjobs"}');var v,$,w;function k(){return k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var a in o)({}).hasOwnProperty.call(o,a)&&(e[a]=o[a])}return e},k.apply(null,arguments)}(0,b.registerBlockType)(C,{icon:function(e){return p.createElement("svg",k({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),v||(v=p.createElement("path",{fill:"#597DFC",d:"M0 0h24v24H0z"})),$||($=p.createElement("path",{fill:"#fff",d:"M18.187 17.903a.702.702 0 1 0 0-1.403.702.702 0 0 0 0 1.403M15.936 16.563a.57.57 0 1 0 0 *********** 0 0 0 0-1.139M19.69 18.158a.266.266 0 0 0-.188.452.265.265 0 1 0 .188-.452M17.088 19.001a.37.37 0 1 0 0-.741.37.37 0 0 0 0 .741M10.434 8.731q.105.028.212.026h.013a.762.762 0 1 0-.344-.086z"})),w||(w=p.createElement("path",{fill:"#fff",d:"M15.52 13.672a5.656 5.656 0 0 0-3.69-8.546A6 6 0 0 0 10.647 5a5.695 5.695 0 0 0-1.404 11.2c-.403-.192-3.104-2.047-3.071-5.673.02-2.336 1.986-4.23 4.323-4.31a4.477 4.477 0 0 1 1.88 8.606c-.344.112-1.535.384-2.522-.96a3.62 3.62 0 0 1-.543-2.33 1.083 1.083 0 0 1 1.066-1.026.6.6 0 0 1 .106.007 1.055 1.055 0 0 1 1.046 1.111 4.1 4.1 0 0 1-.383 1.516l-.026.053a7 7 0 0 1-.106.225.362.362 0 0 0 .39.51h.006a.37.37 0 0 0 .239-.154c.523-.69.823-1.524.86-2.39a1.98 1.98 0 0 0-.55-1.41 2.09 2.09 0 0 0-1.495-.582h-.126a2.5 2.5 0 0 0-.821.18 2.08 2.08 0 0 0-1.344 1.846 4.25 4.25 0 0 0 1.165 3.469q.212.245.463.45.293.237.63.41a2 2 0 0 0 .343.171v-.006q.317.129.656.179h.006c.502.069 1.012 0 1.477-.199a5.7 5.7 0 0 0 1.052-.602l.921.86a1.376 1.376 0 0 1 2.192.457 1.06 1.06 0 0 1 .701-.649z"})))},attributes:{resOption:{type:"string",default:"Desktop"},blockId:{type:"string"},blockMeta:{type:"object"},hideJobHeader:{type:"boolean",default:!1},hideJobList:{type:"boolean",default:!1},hideJobFooter:{type:"boolean",default:!1},cover:{type:"string",default:""}},edit:f,save:e=>(0,p.createElement)(p.Fragment,null,(0,p.createElement)(E.Save,{...e},(0,p.createElement)("div",{className:"ej-career-page"},(0,p.createElement)(j.InnerBlocks.Content,null)))),example:{attributes:{cover:`${EasyJobsLocalize?.image_url}/block-preview/landing.png`}}});const N=({props:e,categories:t,locations:o})=>{const{attributes:a,setAttributes:n}=e,{filterByTitle:l,filterByCategory:s,filterByLocation:r,titleSearchValue:i}=a;return(0,p.createElement)("form",{id:"ejJobFilterForm",className:"ej-job-filter-form job-filter d-flex",action:"",method:"get"},l&&(0,p.createElement)("div",{className:"search-bar"},(0,p.createElement)("input",{type:"text",id:"job_title",name:"job_title",value:i,className:"form-control",placeholder:"Job Title",onChange:e=>n({titleSearchValue:e?.value})})),s&&(0,p.createElement)("div",{className:"select-option category"},(0,p.createElement)("select",{name:"job_category",id:"job_category",onChange:e=>n({categorySearchValue:e?.value})},(0,p.createElement)("option",{value:""},"Select Category"),t?.map(((e,t)=>(0,p.createElement)("option",{value:e?.id},e?.name))))),r&&(0,p.createElement)("div",{className:"select-option locations"},(0,p.createElement)("select",{name:"job_location",id:"job_location",onChange:e=>n({locationSearchValue:e.value})},(0,p.createElement)("option",{value:""},"Select Location"),o?.map(((e,t)=>(0,p.createElement)("option",{value:e?.id},e?.name))))),(0,p.createElement)("div",{className:"d-flex"},(0,p.createElement)("button",{onClick:e=>(e=>{e.preventDefault()})(e),className:"ej-btn ej-info-btn-light mr15",type:"submit"},"Submit"),(0,p.createElement)("button",{id:"ej-reset-form",className:"ej-btn ej-danger-btn",type:"reset"},"Reset")))},x=({jobs:e})=>(0,p.createElement)("div",{className:"custom-job-pagination"},(0,p.createElement)("nav",null,(0,p.createElement)("ul",{className:"pagination"},(0,p.createElement)("li",{className:"page-item","aria-disabled":"true","aria-label":"« Previous"},(0,p.createElement)("a",{onClick:e=>e.preventDefault(),href:"#",className:"page-link","aria-hidden":"true"},(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-arrow-left"}),(0,p.createElement)("span",{className:"pagination-text"},"Prev"))),(()=>{const t=[],o=((e,t)=>{if(!e||!t)return null;const o=[1];if(1===e&&1===t)return o;e>4&&o.push("...");const a=e-2,n=e+2,l=Math.min(t,n);for(let e=a>2?a:2;e<=l;e++)o.push(e);return n+1<t&&o.push("..."),n<t&&o.push(t),o})(e?.current_page,e?.last_page);for(let a=0;a<o?.length;a++)t.push((0,p.createElement)("li",{className:"page-item "+(e?.current_page==o[a]?"active":""),"aria-current":"page"},(0,p.createElement)("a",{onClick:e=>e?.preventDefault(),href:"#",className:"page-link"},o[a])));return t})(),(0,p.createElement)("li",{className:"page-item"},(0,p.createElement)("a",{onClick:e=>e.preventDefault(),className:"page-link",href:"#",rel:"next","aria-label":"Next »"},(0,p.createElement)("span",{className:"pagination-text"},"Next"),(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-arrow-right"})))))),{DynamicInputValueHandler:S,EBDisplayIcon:B}=window.EJControls,T=({props:e,jobsData:t})=>{const{setAttributes:o,attributes:a,companyInfo:n}=e,{titleText:l,hideTitle:s,hideIcon:r,icon:i,applyBtnText:c,filterByTitle:m,filterByCategory:b,filterByLocation:j,showCompanyName:d,showLocation:g,showDateLine:u,showNoOfJob:_}=a;return(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"ej-job-body easyjobs-blocks easyjobs-blocks-job-list"},(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper ej-template-default",id:"easyjobs-list"},(0,p.createElement)("div",{className:"ej-section"},(0,p.createElement)("div",{className:"d-flex ej-job-filter-wrap"},!s&&(0,p.createElement)("div",{className:"ej-section-title"},!r&&(0,p.createElement)(B,{icon:i,className:"ej-section-title-icon"}),(0,p.createElement)("span",{className:"ej-section-title-text"},(0,p.createElement)(S,{placeholder:(0,y.__)("Add title..","essential-blocks"),className:"eb-button-text",value:l,onChange:e=>o({titleText:e}),allowedFormats:["core/bold","core/italic","core/strikethrough"]}))),n?.show_job_filter&&(m||b||j)&&(0,p.createElement)(N,{props:e,categories:t?.categories,locations:t?.locations})),(0,p.createElement)("div",{className:"ej-section-content"},(0,p.createElement)("div",{className:"ej-job-list"},t?.jobs?.data&&t?.jobs?.data?.map(((e,t)=>(0,p.createElement)("div",{className:"ej-job-list-item ej-job-list-item-cat"},(0,p.createElement)("div",{className:"ej-job-list-item-inner"},(0,p.createElement)("div",{className:"ej-job-list-item-col "+(e?.is_pinned?"ej-has-badge":"")},(0,p.createElement)("h2",{className:"ej-job-title"},(0,p.createElement)("a",{href:"#",onClick:e=>e.preventDefault()},e?.title)),(d||g)&&(0,p.createElement)("div",{className:"ej-job-list-info"},d&&(0,p.createElement)("div",{className:"ej-job-list-info-block ej-job-list-company-name"},(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-briefcase-2"}),(0,p.createElement)("a",{onClick:e=>e.preventDefault(),href:"#",target:"_blank"},e?.company_name)),g&&(0,p.createElement)("div",{className:"ej-job-list-info-block ej-job-list-location"},(e?.is_remote||e?.job_address?.city||e?.job_address?.country)&&(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-map-maker"}),e?.is_remote?(0,p.createElement)("span",null,"Anywhere"):(0,p.createElement)("span",null,e.job_address?.city&&e?.job_address?.city?.name,e.job_address?.country&&e.job_address?.city&&", ",e?.job_address?.country&&e?.job_address?.country?.name)))),(u||_)&&(0,p.createElement)("div",{className:"ej-job-list-item-col ej-job-time-col"},e?.is_expired?(0,p.createElement)("p",{className:"ej-list-title ej-expired"},"Expired"):(0,p.createElement)(p.Fragment,null,u&&(0,p.createElement)("p",{className:"ej-deadline"},e?.expire_at),e?.vacancies&&_&&(0,p.createElement)("p",{className:"ej-list-sub"},"No of vacancies: ",e?.vacancies))),(0,p.createElement)("div",{className:"ej-job-list-item-col ej-job-apply-btn"},(0,p.createElement)("a",{href:`${e?.apply_url?e?.apply_url:"#"}`,className:"ej-btn ej-info-btn-light",target:"_blank",onClick:e=>e.preventDefault()},(0,p.createElement)(S,{placeholder:(0,y.__)("Add text..","essential-blocks"),className:"eb-button-text",value:c,onChange:e=>o({applyBtnText:e}),allowedFormats:["core/bold","core/italic","core/strikethrough"]})))))))),t?.jobs&&t?.jobs?.last_page>1&&(0,p.createElement)(x,{jobs:t?.jobs}))))))},{DynamicInputValueHandler:L}=window.EJControls,D=({props:e,jobsData:t})=>{const{setAttributes:o,attributes:a}=e,{titleText:n,hideTitle:l,applyBtnText:s,filterByTitle:r,filterByCategory:i,filterByLocation:c,showCompanyName:m,showLocation:b,showDateLine:j,showNoOfJob:d}=a;return(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"ej-job-body easyjobs-blocks easyjobs-blocks-job-list"},(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper ej-template-classic",id:"easyjobs-list"},(0,p.createElement)("div",{className:"ej-section"},(0,p.createElement)("div",{className:"section__header section__header--flex ej-job-filter-wrap",id:"open_job_position"},!l&&(0,p.createElement)("div",{className:"ej-section-title"},(0,p.createElement)("span",{className:"ej-section-title-text"},(0,p.createElement)(L,{placeholder:(0,y.__)("Add title..","essential-blocks"),className:"eb-button-text",value:n,onChange:e=>o({titleText:e}),allowedFormats:["core/bold","core/italic","core/strikethrough"]}))),(r||i||c)&&(0,p.createElement)(N,{props:e,categories:t?.categories,locations:t?.locations}))),(0,p.createElement)("div",{className:"ej-job-list ej-job-list-classic"},t?.jobs?.data&&t?.jobs?.data?.map(((e,t)=>(0,p.createElement)("div",{className:"ej-job-list-item job__card ej-job-list-item-cat <?php if(isset($job->is_pinned) && $job->is_pinned) echo 'ej-has-badge'?>"},(0,p.createElement)("div",{className:"job__info ej-job-list-item-col"},(0,p.createElement)("h3",{className:"ej-job-title"},(0,p.createElement)("a",{onClick:e=>e.preventDefault(),href:"#"},e?.title)),(0,p.createElement)("p",{className:"meta ej-job-list-info"},m&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-briefcase-2"}," "),(0,p.createElement)("a",{onClick:e=>e.preventDefault(),href:"#",target:"_blank",className:"office__name ej-job-list-info-block"},e?.company_name)),b&&(0,p.createElement)("span",{className:"office__location ej-job-list-info-block"},(e?.is_remote||e?.job_address?.city||e?.job_address?.country)&&(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-map-maker"}),e.is_remote?(0,p.createElement)("span",null," Anywhere"):(0,p.createElement)("span",null," ",e?.job_address?.city&&e?.job_address?.city?.name,e?.job_address?.country&&e?.job_address?.city&&", ",e?.job_address?.country&&e?.job_address?.country?.name)))),d&&e?.vacancies&&(0,p.createElement)("div",{className:"job__vacancy ej-job-list-item-col"},(0,p.createElement)("h4",null,e.vacancies),(0,p.createElement)("p",null,(0,y.__)("No of vacancies ","easyjobs"))),(0,p.createElement)("div",{className:"job__apply ej-job-list-item-col"},(0,p.createElement)("a",{onClick:e=>e.preventDefault(),href:`${e?.apply_url?e?.apply_url:"#"}`,className:"button button__success button__radius",target:"_blank"},(0,p.createElement)(L,{placeholder:(0,y.__)("Add text..","essential-blocks"),className:"eb-button-text",value:s,onChange:e=>o({applyBtnText:e}),allowedFormats:["core/bold","core/italic","core/strikethrough"]})),j&&(0,p.createElement)("span",{className:"deadline ej-deadline"},e?.is_expired?(0,p.createElement)("span",{className:"ej-expired"},(0,y.__)("Expired","easyjobs")):(0,p.createElement)(p.Fragment,null,(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-calender"}),(0,p.createElement)("span",null,(0,y.__)(" Deadline:","easyjobs")," ",e?.expire_at)))))))),t?.jobs&&t?.jobs?.last_page>1&&(0,p.createElement)(x,{jobs:t?.jobs}))))},{DynamicInputValueHandler:P}=window.EJControls,I=({props:e,jobsData:t})=>{const{setAttributes:o,attributes:a}=e,{titleText:n,hideTitle:l,applyBtnText:s,filterByTitle:r,filterByCategory:i,filterByLocation:c,showCompanyName:m,showLocation:b,showDateLine:j,showNoOfJob:d}=a;return(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"ej-job-body easyjobs-blocks easyjobs-blocks-job-list"},(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper ej-template-elegant",id:"easyjobs-list"},(0,p.createElement)("div",{className:"ej-section"},(0,p.createElement)("div",{className:"section__header section__header--flex ej-job-filter-wrap"},!l&&(0,p.createElement)("div",{className:"ej-section-title"},(0,p.createElement)("span",{className:"ej-section-title-text"},(0,p.createElement)(P,{placeholder:(0,y.__)("Add title..","essential-blocks"),className:"eb-button-text",value:n,onChange:e=>o({titleText:e}),allowedFormats:["core/bold","core/italic","core/strikethrough"]}))),(r||i||c)&&(0,p.createElement)(N,{props:e,categories:t?.categories,locations:t?.locations}))),(0,p.createElement)("div",{className:"ej-job-list ej-job-list-elegant"},(0,p.createElement)("div",{className:"ej-row"},t?.jobs?.data&&t?.jobs?.data?.map(((e,t)=>(0,p.createElement)("div",{className:"ej-col-lg-6 ej-job-list-item-cat"},(0,p.createElement)("div",{className:"job__card <?php if(isset($job->is_pinned) && $job->is_pinned) echo 'ej-has-badge'?>"},(0,p.createElement)("h3",{className:"ej-job-title"},(0,p.createElement)("a",{onClick:e=>e.preventDefault(),href:"#"},e?.title)),(0,p.createElement)("p",{className:"meta"},m&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-briefcase"}," "),(0,p.createElement)("a",{onClick:e=>e.preventDefault(),href:"#",className:"office__name"},e?.company_name)),b&&(e?.is_remote||e?.job_address?.city||e?.job_address?.country)&&(0,p.createElement)("span",{className:"office__location"},(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-map-maker"}),e?.is_remote?(0,p.createElement)("span",null,(0,y.__)(" Anywhere","easyjobs")):(0,p.createElement)("span",null," ",e?.job_address?.city&&e?.job_address?.city?.name,e?.job_address?.country&&e?.job_address?.city&&", ",e?.job_address?.country&&e?.job_address?.country?.name))),(0,p.createElement)("div",{className:"job__bottom"},(0,p.createElement)("div",{className:"job__apply"},(0,p.createElement)("a",{onClick:e=>e.preventDefault(),href:`${e?.apply_url?e?.apply_url:"#"}`,className:"button button__primary radius-15",target:"_blank"},(0,p.createElement)(P,{placeholder:(0,y.__)("Add text..","essential-blocks"),className:"eb-button-text",value:s,onChange:e=>o({applyBtnText:e}),allowedFormats:["core/bold","core/italic","core/strikethrough"]}))),d&&e?.vacancies&&(0,p.createElement)("div",{className:"job__vacancy"},(0,p.createElement)("h4",null,e.vacancies),(0,p.createElement)("p",null,(0,y.__)("No of vacancies","easyjobs")))),j&&(0,p.createElement)("span",{className:"deadline"},(0,p.createElement)("i",{className:"ej-icon ej-calender"}),e?.expire_at)))))),t?.jobs&&t?.jobs?.last_page>1&&(0,p.createElement)(x,{jobs:t?.jobs})))))},M=[{label:(0,y.__)("Title","easyjobs"),value:"title"},{label:(0,y.__)("Published date","easyjobs"),value:"published_at"},{label:(0,y.__)("Expired date","easyjobs"),value:"expire_at"},{label:(0,y.__)("Created date","easyjobs"),value:"created_at"}],A=[{label:(0,y.__)("ASC","easyjobs"),value:"asc"},{label:(0,y.__)("DESC","easyjobs"),value:"desc"}],F="blockBg",J=[{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignleft"})),value:"left"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-aligncenter"})),value:"center"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignright"})),value:"right"}],H="blockWidth",O="wrpMargin",R="wrpPadding",q="applyBtnPadding",U="wrpBrdShw",V="jobListBg",z="jobListBrdShw",W="applyBtnBrdShw",G=[{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignleft"})),value:"left"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-aligncenter"})),value:"center"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignright"})),value:"right"}],Z=((0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignleft"})),(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignright"})),(0,y.__)("Fixed","easyjobs"),(0,y.__)("Fixed","easyjobs"),(0,y.__)("Fixed","easyjobs"),(0,y.__)("Fixed","easyjobs"),"jobListTitleIconWidth"),Y="jobListTitleIconHeight",Q="jobListTitleIconSize",K="jobListPadding",X="jobListMargin",ee="spaceBetweenTitleCompany",te="jobListTitleMargin",oe="jobListTitlePadding",ae="job_list_title",ne="submit_btn",le="reset_btn",se="job_title",re="company_name",ie="job_location",ce="no_of_jobs",me="job_deadline",be="apply_btn",{generateResponsiveRangeAttributes:pe,generateTypographyAttributes:je,generateBorderShadowAttributes:ye,generateDimensionsAttributes:de,generateBackgroundAttributes:ge}=window.EJControls,ue={resOption:{type:"string",default:"Desktop"},blockId:{type:"string"},blockMeta:{type:"object"},hideTitle:{type:"boolean",default:!1},titleText:{type:"string",default:(0,y.__)("Open Job Positions","easyjobs")},hideIcon:{type:"boolean",default:!1},icon:{type:"string",default:"fas fa-briefcase"},applyBtnText:{type:"string",default:"Apply"},filterByTitle:{type:"boolean",default:!0},filterByCategory:{type:"boolean",default:!0},filterByLocation:{type:"boolean",default:!0},orderBy:{type:"string",default:"title"},sortBy:{type:"string",default:"asc"},noOfJob:{type:"number",default:2},activeOnly:{type:"boolean",default:!0},showCompanyName:{type:"boolean",default:!0},showLocation:{type:"boolean",default:!0},showDateLine:{type:"boolean",default:!0},showNoOfJob:{type:"boolean",default:!0},contentAlign:{type:"string",default:"center"},addIcon:{type:"boolean",default:!1},iconPosition:{type:"string",default:"left"},iconSize:{type:"string"},iconSpace:{type:"string",default:"5px"},textColor:{type:"string",default:"var(--eb-global-button-text-color)"},jobListTitleColor:{type:"string",default:"#2f323e"},jobListTitleIconBgColor:{type:"string",default:"rgba(89,125,252,.1)"},jobListTitleIconColor:{type:"string",default:"#597dfc"},applyBtnTextColor:{type:"string",default:""},applyBtnBgColor:{type:"string",default:""},applyBtnTextColorH:{type:"string",default:""},applyBtnBgColorH:{type:"string",default:""},submitTextColor:{type:"string",default:"#ff9635"},submitBgColor:{type:"string",default:"rgba(255,150,53,.1)"},submitTextColorH:{type:"string",default:"#ff9635"},submitBgColorH:{type:"string",default:"rgba(255,150,53,.1)"},jobTitleColor:{type:"string",default:""},resetTextColor:{type:"string",default:"#ff5f74"},resetBgColor:{type:"string",default:"rgba(255,95,116,.1)"},resetTextColorH:{type:"string",default:"#ff5f74"},resetBgColorH:{type:"string",default:"rgba(255,95,116,.1)"},buttonURL:{type:"string",default:"Apply"},titleSearchValue:{type:"string",default:""},categorySearchValue:{type:"string",default:"Select Category"},locationSearchValue:{type:"string",default:"Select Location"},newWindow:{type:"boolean",default:!1},addNofollow:{type:"boolean",default:!1},applyButtonAlign:{type:"string",default:"center"},buttonAlign:{type:"string",default:"center"},jobListTitleIconSize:{type:"string",default:"fixed"},buttonWidth:{type:"string",default:"auto"},jobListTitleIconWidth:{type:"string",default:"fixed"},jobListTitleIconHeight:{type:"string",default:"fixed"},hoverEffect:{type:"string"},hoverTextColor:{type:"string",default:"var(--eb-global-button-text-color)"},separatorColor:{type:"string",default:"#f5f7fd"},companyNameColor:{type:"string",default:""},jobLocationColor:{type:"string",default:""},jobDeadlineColor:{type:"string",default:""},jobVacancyColor:{type:"string",default:""},hoverTransition:{type:"number",default:.3},submitStyle:{type:"string",default:"normal"},applyBtnStyle:{type:"string",default:"normal"},resetStyle:{type:"string",default:"normal"},selectedTemplate:{type:"string",default:"default"},cover:{type:"string",default:""},...je(Object.values(t)),...pe(H,{defaultRange:100,defaultUnit:"%"}),...pe(Q,{defaultRange:20,defaultUnit:"px"}),...pe(Z,{defaultRange:50,defaultUnit:"px"}),...pe(Z,{defaultRange:50,defaultUnit:"px"}),...pe(Y,{defaultRange:50,defaultUnit:"px"}),...pe("iconSize",{noUnits:!0}),...pe("iconSpace",{defaultRange:8,noUnits:!0}),...de(R,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...de(q,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...de(K,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...de(X,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...de(ee,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...de(te,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...de(oe,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...de(O,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...ge(F,{noOverlay:!0,noMainBgi:!0,defaultFillColor:"#f9f9f9",defaultHovFillColor:"#f9f9f9"}),...ge(V,{noOverlay:!0,noMainBgi:!0,defaultFillColor:"#fff",defaultHovFillColor:"#fff"}),...ye(U),...ye(z),...ye(W)},{ColorControl:_e,ResponsiveRangeController:he,ResponsiveDimensionsControl:fe,TypographyDropdown:Ee,BackgroundControl:Ce,BorderShadowControl:ve,DynamicInputControl:$e,EBIconPicker:we}=window.EJControls,ke=({attributes:e,setAttributes:t,makeAjaxCall:o,setJobsData:a})=>{const{resOption:n,hideTitle:l,titleText:s,hideIcon:r,icon:i,applyBtnText:c,filterByTitle:m,filterByCategory:b,filterByLocation:u,orderBy:_,sortBy:h,noOfJob:f,activeOnly:E,showCompanyName:C,showLocation:v,showDateLine:$,showNoOfJob:w,contentAlign:k,applyButtonAlign:N,jobListTitleIconWidth:x,jobListTitleIconHeight:S,jobListTitleIconSize:B,separatorColor:T,submitStyle:L,applyBtnStyle:D,applyBtnTextColor:P,applyBtnBgColor:I,applyBtnTextColorH:pe,applyBtnBgColorH:je,resetStyle:ye,jobListTitleColor:de,jobListTitleIconBgColor:ge,jobListTitleIconColor:ke,submitTextColor:Ne,submitBgColor:xe,submitTextColorH:Se,submitBgColorH:Be,jobTitleColor:Te,resetTextColor:Le,resetBgColor:De,resetTextColorH:Pe,resetBgColorH:Ie,companyNameColor:Me,jobLocationColor:Ae,jobDeadlineColor:Fe,jobVacancyColor:Je}=e,He={setAttributes:t,resOption:n,attributes:e,objAttributes:ue};return(0,p.createElement)(j.InspectorControls,{key:"controls"},(0,p.createElement)("div",{className:"eb-panel-control"},(0,p.createElement)(d.TabPanel,{className:"eb-parent-tab-panel",activeClass:"active-tab",tabs:[{name:"content",title:(0,y.__)("Content","easyjobs"),className:"eb-tab general"},{name:"styles",title:(0,y.__)("Style","easyjobs"),className:"eb-tab styles"},{name:"advanced",title:(0,y.__)("Advanced","easyjobs"),className:"eb-tab advance"}]},(e=>(0,p.createElement)("div",{className:"eb-tab-controls "+e.name},"content"===e.name&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.PanelBody,{title:(0,y.__)("General","easyjobs"),initialOpen:!1},(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Hide title","easyjobs"),checked:l,onChange:()=>t({hideTitle:!l})}),!l&&(0,p.createElement)($e,{label:"Title",attrName:"titleText",inputValue:s,setAttributes:t,onChange:e=>t({titleText:e})}),!l&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Hide icon","easyjobs"),checked:r,onChange:()=>t({hideIcon:!r})}),!r&&(0,p.createElement)(we,{value:i,onChange:e=>{t({icon:e})}})),(0,p.createElement)($e,{label:"Apply button text",attrName:"applyBtnText",inputValue:c,setAttributes:t,onChange:e=>t({applyBtnText:e})})),(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Job Filter","easyjobs"),initialOpen:!1},(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Show search by title","easyjobs"),checked:m,onChange:()=>t({filterByTitle:!m})}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Show search by category","easyjobs"),checked:b,onChange:()=>t({filterByCategory:!b})}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Show search by location","easyjobs"),checked:u,onChange:()=>t({filterByLocation:!u})})),(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Job List","easyjobs"),initialOpen:!1},(0,p.createElement)(d.SelectControl,{label:(0,y.__)("Order by","easyjobs"),value:_,options:M,onChange:e=>{t({orderBy:e})}}),(0,p.createElement)(d.SelectControl,{label:(0,y.__)("Sort by","easyjobs"),value:h,options:A,onChange:e=>{t({sortBy:e})}}),(0,p.createElement)(d.RangeControl,{label:(0,y.__)("Show jobs","easyjobs"),value:f,onChange:e=>{t({noOfJob:e})},step:1,min:1,max:1e4}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Show open jobs only","easyjobs"),checked:E,onChange:()=>{t({activeOnly:!E})}}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Show company name","easyjobs"),checked:C,onChange:()=>t({showCompanyName:!C})}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Show company address","easyjobs"),checked:v,onChange:()=>t({showLocation:!v})}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Show Deadline","easyjobs"),checked:$,onChange:()=>t({showDateLine:!$})}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Show No of Vacancies","easyjobs"),checked:w,onChange:()=>t({showNoOfJob:!w})}))),"styles"===e.name&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.PanelBody,{title:(0,y.__)("General","easyjobs"),initialOpen:!1},(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Background","easyjobs"))),(0,p.createElement)(Ce,{controlName:F,resRequiredProps:He,noOverlay:!0,noMainBgi:!0}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,{label:(0,y.__)("Alignment","easyjobs")},(0,p.createElement)(d.ButtonGroup,{id:"eb-button-group-alignment"},J.map(((e,o)=>(0,p.createElement)(d.Button,{key:o,isPrimary:k===e.value,isSecondary:k!==e.value,onClick:()=>t({contentAlign:e.value})},e.label))))),(0,p.createElement)(he,{baseLabel:(0,y.__)("Width","easyjobs"),controlName:H,resRequiredProps:He,min:0,max:100,step:1}),(0,p.createElement)(fe,{resRequiredProps:He,controlName:O,baseLabel:(0,y.__)("Margin","easyjobs")}),(0,p.createElement)(fe,{resRequiredProps:He,controlName:R,baseLabel:(0,y.__)("Form Padding","easyjobs")}),(0,p.createElement)(ve,{controlName:U,noBorder:!1,resRequiredProps:He}))),(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Section","easyjobs"),initialOpen:!1},(0,p.createElement)(p.Fragment,null,(0,p.createElement)(fe,{resRequiredProps:He,controlName:te,baseLabel:(0,y.__)("Margin","easyjobs")}),(0,p.createElement)(fe,{resRequiredProps:He,controlName:oe,baseLabel:(0,y.__)("Padding","easyjobs")}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Section Heading","easyjobs"))),(0,p.createElement)(_e,{label:(0,y.__)("Color","easyjobs"),color:de,onChange:e=>t({jobListTitleColor:e})}),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:ae,resRequiredProps:He}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Section Icon","easyjobs"))),(0,p.createElement)(d.BaseControl,{label:(0,y.__)("Width","easyjobs")}),"fixed"===x&&(0,p.createElement)(he,{baseLabel:(0,y.__)("Width","easyjobs"),controlName:Z,resRequiredProps:He,min:50,max:100,step:5}),(0,p.createElement)(d.BaseControl,{label:(0,y.__)("Height","easyjobs")}),"fixed"===S&&(0,p.createElement)(he,{baseLabel:(0,y.__)("Height","easyjobs"),controlName:Y,resRequiredProps:He,min:50,max:100,step:5}),(0,p.createElement)(_e,{label:(0,y.__)("Background","easyjobs"),color:ge,onChange:e=>t({jobListTitleIconBgColor:e})}),(0,p.createElement)(_e,{label:(0,y.__)("Color","easyjobs"),color:ke,onChange:e=>t({jobListTitleIconColor:e})}),(0,p.createElement)(d.BaseControl,{label:(0,y.__)("Icon Size","easyjobs")}),"fixed"===B&&(0,p.createElement)(he,{baseLabel:(0,y.__)("Size","easyjobs"),controlName:Q,resRequiredProps:He,min:10,max:50,step:5}))),(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Job Filter","easyjobs"),initialOpen:!1},(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Submit Button","easyjobs"))),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:ne,resRequiredProps:He}),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(d.ButtonGroup,null,[{label:(0,y.__)("Normal","easyjobs"),value:"normal"},{label:(0,y.__)("Hover","easyjobs"),value:"hover"}].map((({value:e,label:o},a)=>(0,p.createElement)(d.Button,{key:"submitBtn"+a,isPrimary:L===e,isSecondary:L!==e,onClick:()=>t({submitStyle:e})},o))))),"normal"===L&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(_e,{label:(0,y.__)("Text Color","easyjobs"),color:Ne,onChange:e=>t({submitTextColor:e})}),(0,p.createElement)(_e,{label:(0,y.__)("Background Color","easyjobs"),color:xe,onChange:e=>t({submitBgColor:e})}))),"hover"===L&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(_e,{label:(0,y.__)("Text Color","easyjobs"),color:Se,onChange:e=>t({submitTextColorH:e})}),(0,p.createElement)(_e,{label:(0,y.__)("Background Color","easyjobs"),color:Be,onChange:e=>t({submitBgColorH:e})})))),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Reset Button","easyjobs"))),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:le,resRequiredProps:He}),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(d.ButtonGroup,null,[{label:(0,y.__)("Normal","easyjobs"),value:"normal"},{label:(0,y.__)("Hover","easyjobs"),value:"hover"}].map((({value:e,label:o},a)=>(0,p.createElement)(d.Button,{key:a,isPrimary:ye===e,isSecondary:ye!==e,onClick:()=>t({resetStyle:e})},o))))),"normal"===ye&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(_e,{label:(0,y.__)("Text Color","easyjobs"),color:Le,onChange:e=>t({resetTextColor:e})}),(0,p.createElement)(_e,{label:(0,y.__)("Background Color","easyjobs"),color:De,onChange:e=>t({resetBgColor:e})}))),"hover"===ye&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(_e,{label:(0,y.__)("Text Color h","easyjobs"),color:Pe,onChange:e=>t({resetTextColorH:e})}),(0,p.createElement)(_e,{label:(0,y.__)("Background Color","easyjobs"),color:Ie,onChange:e=>t({resetBgColorH:e})}))))),(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Job List","easyjobs"),initialOpen:!1},(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Background","easyjobs"))),(0,p.createElement)(Ce,{controlName:V,resRequiredProps:He,noOverlay:!0,noMainBgi:!0}),(0,p.createElement)(_e,{label:(0,y.__)("Separator Color","easyjobs"),color:T,onChange:e=>t({separatorColor:e})}),(0,p.createElement)(fe,{resRequiredProps:He,controlName:K,baseLabel:(0,y.__)("Padding","easyjobs")}),(0,p.createElement)(fe,{resRequiredProps:He,controlName:X,baseLabel:(0,y.__)("Margin","easyjobs")}),(0,p.createElement)(ve,{controlName:z,resRequiredProps:He}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Job Title","easyjobs"))),(0,p.createElement)(_e,{label:(0,y.__)("Color","easyjobs"),color:Te,onChange:e=>t({jobTitleColor:e})}),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:se,resRequiredProps:He}),(0,p.createElement)(fe,{resRequiredProps:He,controlName:ee,baseLabel:(0,y.__)("Space","easyjobs")}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Company Name","easyjobs"))),(0,p.createElement)(_e,{label:(0,y.__)("Color","easyjobs"),color:Me,onChange:e=>t({companyNameColor:e})}),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:re,resRequiredProps:He}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Job Location","easyjobs"))),(0,p.createElement)(_e,{label:(0,y.__)("Color","easyjobs"),color:Ae,onChange:e=>t({jobLocationColor:e})}),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:ie,resRequiredProps:He}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Job Deadline","easyjobs"))),(0,p.createElement)(_e,{label:(0,y.__)("Color","easyjobs"),color:Fe,onChange:e=>t({jobDeadlineColor:e})}),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:me,resRequiredProps:He}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Job Vacancies","easyjobs"))),(0,p.createElement)(_e,{label:(0,y.__)("Color","easyjobs"),color:Je,onChange:e=>t({jobVacancyColor:e})}),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:ce,resRequiredProps:He}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Job Apply Button","easyjobs"))),(0,p.createElement)(Ee,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:be,resRequiredProps:He}),(0,p.createElement)(d.BaseControl,{label:(0,y.__)("Alignment","easyjobs")},(0,p.createElement)(d.ButtonGroup,{id:"eb-button-group-alignment"},G.map(((e,o)=>(0,p.createElement)(d.Button,{key:o,isPrimary:N===e.value,isSecondary:N!==e.value,onClick:()=>t({applyButtonAlign:e.value})},e.label))))),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(d.ButtonGroup,null,[{label:(0,y.__)("Normal","easyjobs"),value:"normal"},{label:(0,y.__)("Hover","easyjobs"),value:"hover"}].map((({value:e,label:o},a)=>(0,p.createElement)(d.Button,{key:"submitBtn"+a,isPrimary:D===e,isSecondary:D!==e,onClick:()=>t({applyBtnStyle:e})},o))))),"normal"===D&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(_e,{label:(0,y.__)("Text Color","easyjobs"),color:P,onChange:e=>t({applyBtnTextColor:e})}),(0,p.createElement)(_e,{label:(0,y.__)("Background Color","easyjobs"),color:I,onChange:e=>t({applyBtnBgColor:e})}))),"hover"===D&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.BaseControl,null,(0,p.createElement)(_e,{label:(0,y.__)("Text Color","easyjobs"),color:pe,onChange:e=>t({applyBtnTextColorH:e})}),(0,p.createElement)(_e,{label:(0,y.__)("Background Color","easyjobs"),color:je,onChange:e=>t({applyBtnBgColorH:e})}))),(0,p.createElement)(ve,{controlName:W,resRequiredProps:He}),(0,p.createElement)(fe,{resRequiredProps:He,controlName:q,baseLabel:(0,y.__)("Padding","easyjobs")})))))))),(0,p.createElement)(g,{link:"https://easy.jobs/docs/display-job-listings-on-your-career-page/"}))},{softMinifyCssStrings:Ne,generateDimensionsControlStyles:xe,generateBorderShadowStyles:Se,generateTypographyStyles:Be,generateBackgroundControlStyles:Te,generateResponsiveRangeStyles:Le,StyleComponent:De}=window.EJControls;function Pe(e){const{attributes:t,setAttributes:o,name:a}=e,{contentAlign:n,jobListTitleIconWidth:l,jobListTitleIconHeight:s,jobListTitleIconSize:r,jobTitleColor:i,applyButtonAlign:c,separatorColor:m,hoverTransition:b,jobListTitleColor:j,jobListTitleIconBgColor:y,jobListTitleIconColor:d,submitTextColor:g,submitBgColor:u,submitTextColorH:_,submitBgColorH:h,resetTextColor:f,resetBgColor:E,resetTextColorH:C,resetBgColorH:v,companyNameColor:$,jobLocationColor:w,jobDeadlineColor:k,jobVacancyColor:N,applyBtnTextColor:x,applyBtnBgColor:S,applyBtnTextColorH:B,applyBtnBgColorH:T}=t,{dimensionStylesDesktop:L,dimensionStylesTab:D,dimensionStylesMobile:P}=xe({controlName:R,styleFor:"padding",attributes:t}),{dimensionStylesDesktop:I,dimensionStylesTab:M,dimensionStylesMobile:A}=xe({controlName:q,styleFor:"padding",attributes:t}),{dimensionStylesDesktop:J,dimensionStylesTab:G,dimensionStylesMobile:pe}=xe({controlName:K,styleFor:"padding",attributes:t}),{dimensionStylesDesktop:je,dimensionStylesTab:ye,dimensionStylesMobile:de}=xe({controlName:X,styleFor:"margin",attributes:t}),{dimensionStylesDesktop:ge,dimensionStylesTab:ue,dimensionStylesMobile:_e}=xe({controlName:te,styleFor:"margin",attributes:t}),{dimensionStylesDesktop:he,dimensionStylesTab:fe,dimensionStylesMobile:Ee}=xe({controlName:ee,styleFor:"padding-bottom",attributes:t}),{dimensionStylesDesktop:Ce,dimensionStylesTab:ve,dimensionStylesMobile:$e}=xe({controlName:oe,styleFor:"padding",attributes:t}),{rangeStylesDesktop:we,rangeStylesTab:ke,rangeStylesMobile:Pe}=Le({controlName:H,property:"width",attributes:t}),{rangeStylesDesktop:Ie,rangeStylesTab:Me,rangeStylesMobile:Ae}=Le({controlName:Z,property:"width",attributes:t}),{rangeStylesDesktop:Fe,rangeStylesTab:Je,rangeStylesMobile:He}=Le({controlName:Y,property:"height",attributes:t}),{rangeStylesDesktop:Oe,rangeStylesTab:Re,rangeStylesMobile:qe}=Le({controlName:Q,property:"font-size",attributes:t,customUnit:"px"}),{backgroundStylesDesktop:Ue,hoverBackgroundStylesDesktop:Ve,backgroundStylesTab:ze,hoverBackgroundStylesTab:We,backgroundStylesMobile:Ge,hoverBackgroundStylesMobile:Ze,bgTransitionStyle:Ye}=Te({attributes:t,controlName:V}),{backgroundStylesDesktop:Qe,hoverBackgroundStylesDesktop:Ke,backgroundStylesTab:Xe,hoverBackgroundStylesTab:et,backgroundStylesMobile:tt,hoverBackgroundStylesMobile:ot,bgTransitionStyle:at}=Te({attributes:t,controlName:F}),{styesDesktop:nt,styesTab:lt,styesMobile:st,stylesHoverDesktop:rt,stylesHoverTab:it,stylesHoverMobile:ct,transitionStyle:mt}=Se({controlName:U,attributes:t}),{styesDesktop:bt,styesTab:pt,styesMobile:jt,stylesHoverDesktop:yt,stylesHoverTab:dt,stylesHoverMobile:gt,transitionStyle:ut}=Se({controlName:z,attributes:t}),{styesDesktop:_t,styesTab:ht,styesMobile:ft,stylesHoverDesktop:Et,stylesHoverTab:Ct,stylesHoverMobile:vt,transitionStyle:$t}=Se({controlName:W,attributes:t}),{typoStylesDesktop:wt,typoStylesTab:kt,typoStylesMobile:Nt}=Be({attributes:t,prefixConstant:ae}),{typoStylesDesktop:xt,typoStylesTab:St,typoStylesMobile:Bt}=Be({attributes:t,prefixConstant:ne}),{typoStylesDesktop:Tt,typoStylesTab:Lt,typoStylesMobile:Dt}=Be({attributes:t,prefixConstant:le}),{typoStylesDesktop:Pt,typoStylesTab:It,typoStylesMobile:Mt}=Be({attributes:t,prefixConstant:se}),{typoStylesDesktop:At,typoStylesTab:Ft,typoStylesMobile:Jt}=Be({attributes:t,prefixConstant:re}),{typoStylesDesktop:Ht,typoStylesTab:Ot,typoStylesMobile:Rt}=Be({attributes:t,prefixConstant:ie}),{typoStylesDesktop:qt,typoStylesTab:Ut,typoStylesMobile:Vt}=Be({attributes:t,prefixConstant:me}),{typoStylesDesktop:zt,typoStylesTab:Wt,typoStylesMobile:Gt}=Be({attributes:t,prefixConstant:ce}),{typoStylesDesktop:Zt,typoStylesTab:Yt,typoStylesMobile:Qt}=Be({attributes:t,prefixConstant:be}),{dimensionStylesDesktop:Kt,dimensionStylesTab:Xt,dimensionStylesMobile:eo}=xe({controlName:O,styleFor:"margin",attributes:t}),to=`\n\t\t.easyjobs-blocks {\n            ${D};\n            ${Xt};\n            ${lt};\n            transition: ${b?`all ${b}s,`:""} \n            ${at}, ${mt}; \n            ${Xe};\n            ${"center"===n?"display: flex; justify-content: center;":""}\n            ${"left"===n?"display: flex; justify-content: flex-start;":""}\n            ${"right"===n?"display: flex; justify-content: flex-end;":""}\n        }\n        .easyjobs-shortcode-wrapper {\n            ${ke};\n        }\n        .easyjobs-blocks:hover {\n            ${et}\n        }\n\n        \n        .easyjobs-shortcode-wrapper .ej-job-filter-wrap .ej-section-title {\n            ${ue};\n            ${ve};\n        }\n        .ej-section .ej-section-title .ej-section-title-text {\n            ${j?`color: ${j} !important;`:""}\n            ${kt||""}\n        }\n\t\t${"fixed"==l?`.ej-section .ej-section-title .ej-section-title-icon {\n                ${Me} !important\n            }`:""}\n        ${"fixed"==s?`.ej-section .ej-section-title .ej-section-title-icon {\n                ${Je} !important\n            }`:""}\n        ${"fixed"==r?`.ej-section .ej-section-title i.ej-section-title-icon {\n                ${Re} !important\n            }`:""}\n        .ej-section-title .ej-section-title-icon {\n            ${y?`background-color: ${y} !important;`:""}\n            ${d?`color: ${d} !important;`:""}\n        }\n\n\n        .ej-job-filter-wrap .ej-job-filter-form .ej-info-btn-light {\n            ${u?`background-color: ${u} !important;`:""}\n            ${g?`color: ${g} !important;`:""}\n            ${St||""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-info-btn-light:hover {\n            ${h?`background-color: ${h} !important;`:""}\n            ${_?`color: ${_} !important;`:""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-danger-btn {\n            ${E?`background-color: ${E} !important;`:""}\n            ${f?`color: ${f} !important;`:""}\n            ${Lt||""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-danger-btn:hover {\n            ${v?`background-color: ${v} !important;`:""}\n            ${C?`color: ${C} !important;`:""}\n        }\n\n\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item,\n        .ej-template-elegant .ej-job-list-elegant .job__card {\n            ${ze};\n            ${pt};\n            ${G||""}\n            ${ye||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item:hover,\n        .ej-template-elegant .ej-job-list-elegant .job__card:hover    {\n            ${We}\n            ${dt};\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col {\n            ${m?`border-color: ${m} !important;`:""}\n        }\n        .ej-section-content .ej-job-list .ej-job-title a,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info h3 a,\n        .ej-template-elegant .job__card h3 a {\n            ${It||""}\n            ${i?`color: ${i} !important;`:""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-title,\n        .ej-template-elegant .job__card .ej-job-title {\n            ${fe||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-info-block.ej-job-list-company-name a,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info .meta a,\n        .ej-template-elegant .job__card .meta a {\n            ${$?`color: ${$} !important;`:""}\n            ${Ft||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-info-block.ej-job-list-location span,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info .meta span span,\n        .ej-template-elegant .job__card .meta span span {\n            ${w?`color: ${w} !important;`:""}\n            ${Ot||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col .ej-deadline,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__apply .deadline span,\n        .ej-template-elegant .job__card .deadline {\n            ${k?`color: ${k} !important;`:""}\n            ${Ut||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col .ej-list-sub,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__vacancy h4,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__vacancy p,\n        .ej-template-elegant .job__card .job__bottom .job__vacancy h4,\n        .ej-template-elegant .job__card .job__bottom .job__vacancy p {\n            ${N?`color: ${N} !important;`:""}\n            ${Wt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-btn.ej-info-btn-light,\n        .easyjobs-shortcode-wrapper.ej-template-classic .button__success,\n        .ej-template-elegant .button.button__primary.radius-15 {\n            ${Yt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-apply-btn {\n            text-align: ${c} !important\n        }\n        .ej-job-list-item-inner .ej-job-apply-btn .ej-btn.ej-info-btn-light {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${ht||""}\n            ${M||""}\n        }\n        .ej-job-list-item-inner .ej-job-apply-btn .ej-btn.ej-info-btn-light:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${Ct||""}\n        }\n        .ej-template-classic .ej-job-list-classic .ej-job-list-item .job__apply .button__success.button__radius {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${ht||""}\n            ${M||""}\n        }\n        .ej-template-classic .ej-job-list-classic .ej-job-list-item .job__apply .button__success.button__radius:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${ht||""}\n        }\n        .ej-template-elegant .ej-job-list-elegant .ej-job-list-item-cat .job__apply .button.button__primary.radius-15 {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${ht||""}\n            ${M||""}\n        }\n        .ej-template-elegant .ej-job-list-elegant .ej-job-list-item-cat .job__apply .button.button__primary.radius-15:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${ht||""}\n        }\n\t`,oo=`\n\t\t.easyjobs-blocks {\n            ${P};\n            ${eo};\n            ${st};\n            transition: ${b?`all ${b}s,`:""} \n            ${at}, ${mt}; \n            ${tt};\n            ${"center"===n?"display: flex; justify-content: center;":""}\n            ${"left"===n?"display: flex; justify-content: flex-start;":""}\n            ${"right"===n?"display: flex; justify-content: flex-end;":""}\n        }\n        .easyjobs-shortcode-wrapper {\n            ${Pe};\n        }\n        .easyjobs-blocks:hover {\n            ${ot}\n        }\n\n        \n        .easyjobs-shortcode-wrapper .ej-job-filter-wrap .ej-section-title {\n            ${_e};\n            ${$e};\n        }\n        .ej-section .ej-section-title .ej-section-title-text {\n            ${j?`color: ${j} !important;`:""}\n            ${Nt||""}\n        }\n\t\t${"fixed"==l?`.ej-section .ej-section-title .ej-section-title-icon {\n                ${Ae} !important\n            }`:""}\n        ${"fixed"==s?`.ej-section .ej-section-title .ej-section-title-icon {\n                ${He} !important\n            }`:""}\n        ${"fixed"==r?`.ej-section .ej-section-title i.ej-section-title-icon {\n                ${qe} !important\n            }`:""}\n        .ej-section-title .ej-section-title-icon {\n            ${y?`background-color: ${y} !important;`:""}\n            ${d?`color: ${d} !important;`:""}\n        }\n\n\n        .ej-job-filter-wrap .ej-job-filter-form .ej-info-btn-light {\n            ${u?`background-color: ${u} !important;`:""}\n            ${g?`color: ${g} !important;`:""}\n            ${Bt||""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-info-btn-light:hover {\n            ${h?`background-color: ${h} !important;`:""}\n            ${_?`color: ${_} !important;`:""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-danger-btn {\n            ${E?`background-color: ${E} !important;`:""}\n            ${f?`color: ${f} !important;`:""}\n            ${Dt||""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-danger-btn:hover {\n            ${v?`background-color: ${v} !important;`:""}\n            ${C?`color: ${C} !important;`:""}\n        }\n\n\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item,\n        .ej-template-elegant .ej-job-list-elegant .job__card {\n            ${Ge};\n            ${jt};\n            ${pe||""}\n            ${de||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item:hover,\n        .ej-template-elegant .ej-job-list-elegant .job__card:hover    {\n            ${Ze}\n            ${gt};\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col {\n            ${m?`border-color: ${m} !important;`:""}\n        }\n        .ej-section-content .ej-job-list .ej-job-title a,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info h3 a,\n        .ej-template-elegant .job__card h3 a {\n            ${Mt||""}\n            ${i?`color: ${i} !important;`:""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-title,\n        .ej-template-elegant .job__card .ej-job-title {\n            ${Ee||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-info-block.ej-job-list-company-name a,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info .meta a,\n        .ej-template-elegant .job__card .meta a {\n            ${$?`color: ${$} !important;`:""}\n            ${Jt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-info-block.ej-job-list-location span,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info .meta span span,\n        .ej-template-elegant .job__card .meta span span {\n            ${w?`color: ${w} !important;`:""}\n            ${Rt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col .ej-deadline,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__apply .deadline span,\n        .ej-template-elegant .job__card .deadline {\n            ${k?`color: ${k} !important;`:""}\n            ${Vt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col .ej-list-sub,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__vacancy h4,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__vacancy p,\n        .ej-template-elegant .job__card .job__bottom .job__vacancy h4,\n        .ej-template-elegant .job__card .job__bottom .job__vacancy p {\n            ${N?`color: ${N} !important;`:""}\n            ${Gt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-btn.ej-info-btn-light,\n        .easyjobs-shortcode-wrapper.ej-template-classic .button__success,\n        .ej-template-elegant .button.button__primary.radius-15 {\n            ${Qt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-apply-btn {\n            text-align: ${c} !important\n        }\n        .ej-job-list-item-inner .ej-job-apply-btn .ej-btn.ej-info-btn-light {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${ft||""}\n            ${A||""}\n        }\n        .ej-job-list-item-inner .ej-job-apply-btn .ej-btn.ej-info-btn-light:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${vt||""}\n        }\n        .ej-template-classic .ej-job-list-classic .ej-job-list-item .job__apply .button__success.button__radius {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${ft||""}\n            ${A||""}\n        }\n        .ej-template-classic .ej-job-list-classic .ej-job-list-item .job__apply .button__success.button__radius:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${ft||""}\n        }\n        .ej-template-elegant .ej-job-list-elegant .ej-job-list-item-cat .job__apply .button.button__primary.radius-15 {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${ft||""}\n            ${A||""}\n        }\n        .ej-template-elegant .ej-job-list-elegant .ej-job-list-item-cat .job__apply .button.button__primary.radius-15:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${ft||""}\n        }\n\t`,ao=Ne(`\n\t\t\t\n        .easyjobs-blocks {\n            ${L};\n            ${Kt};\n            ${nt};\n            transition: ${b?`all ${b}s,`:""} \n            ${at}, ${mt}; \n            ${Qe};\n            ${"center"===n?"display: flex; justify-content: center;":""}\n            ${"left"===n?"display: flex; justify-content: flex-start;":""}\n            ${"right"===n?"display: flex; justify-content: flex-end;":""}\n        }\n        .easyjobs-shortcode-wrapper {\n            ${we};\n        }\n        .easyjobs-blocks:hover {\n            ${Ke}\n        }\n\n\n        .easyjobs-shortcode-wrapper .ej-job-filter-wrap .ej-section-title {\n            ${ge};\n            ${Ce};\n        }\n        .ej-section .ej-section-title .ej-section-title-text {\n            ${j?`color: ${j} !important;`:""}\n            ${wt||""}\n        }\n\t\t${"fixed"==l?`.ej-section .ej-section-title .ej-section-title-icon {\n                ${Ie} !important\n            }`:""}\n        ${"fixed"==s?`.ej-section .ej-section-title .ej-section-title-icon {\n                ${Fe} !important\n            }`:""}\n        ${"fixed"==r?`.ej-section .ej-section-title .ej-section-title-icon {\n                ${Oe} !important\n            }`:""}\n        .ej-section-title .ej-section-title-icon {\n            ${y?`background-color: ${y} !important;`:""}\n            ${d?`color: ${d} !important;`:""}\n        }\n\n\n        .ej-job-filter-wrap .ej-job-filter-form .ej-info-btn-light {\n            ${u?`background-color: ${u} !important;`:""}\n            ${g?`color: ${g} !important;`:""}\n            ${xt||""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-info-btn-light:hover {\n            ${h?`background-color: ${h} !important;`:""}\n            ${_?`color: ${_} !important;`:""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-danger-btn {\n            ${E?`background-color: ${E} !important;`:""}\n            ${f?`color: ${f} !important;`:""}\n            ${Tt||""}\n        }\n        .ej-job-filter-wrap .ej-job-filter-form .ej-danger-btn:hover {\n            ${v?`background-color: ${v} !important;`:""}\n            ${C?`color: ${C} !important;`:""}\n        }\n\n\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item,\n        .ej-template-elegant .ej-job-list-elegant .job__card {\n            ${Ue};\n            ${bt};\n            ${J||""}\n            ${je||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item:hover,\n        .ej-template-elegant .ej-job-list-elegant .job__card:hover    {\n            ${Ve}\n            ${yt};\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col {\n            ${m?`border-color: ${m} !important;`:""}\n        }\n        .ej-section-content .ej-job-list .ej-job-title a,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info h3 a,\n        .ej-template-elegant .job__card h3 a {\n            ${Pt||""}\n            ${i?`color: ${i} !important;`:""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-title,\n        .ej-template-elegant .job__card .ej-job-title {\n            ${he||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-info-block.ej-job-list-company-name a,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info .meta a,\n        .ej-template-elegant .job__card .meta a {\n            ${$?`color: ${$} !important;`:""}\n            ${At||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-info-block.ej-job-list-location span,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__info .meta span span,\n        .ej-template-elegant .job__card .meta span span {\n            ${w?`color: ${w} !important;`:""}\n            ${Ht||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col .ej-deadline,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__apply .deadline span,\n        .ej-template-elegant .job__card .deadline {\n            ${k?`color: ${k} !important;`:""}\n            ${qt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-job-list-item .ej-job-list-item-inner .ej-job-list-item-col .ej-list-sub,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__vacancy h4,\n        .easyjobs-shortcode-wrapper.ej-template-classic .job__card .job__vacancy p,\n        .ej-template-elegant .job__card .job__bottom .job__vacancy h4,\n        .ej-template-elegant .job__card .job__bottom .job__vacancy p {\n            ${N?`color: ${N} !important;`:""}\n            ${zt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-list .ej-btn.ej-info-btn-light,\n        .easyjobs-shortcode-wrapper.ej-template-classic .button__success,\n        .ej-template-elegant .button.button__primary.radius-15 {\n            ${Zt||""}\n        }\n        .easyjobs-shortcode-wrapper .ej-job-apply-btn {\n            text-align: ${c} !important\n        }\n        .ej-job-list-item-inner .ej-job-apply-btn .ej-btn.ej-info-btn-light {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${_t||""}\n            ${I||""}\n        }\n        .ej-job-list-item-inner .ej-job-apply-btn .ej-btn.ej-info-btn-light:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${Et||""}\n        }\n        .ej-template-classic .ej-job-list-classic .ej-job-list-item .job__apply .button__success.button__radius {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${_t||""}\n            ${I||""}\n        }\n        .ej-template-classic .ej-job-list-classic .ej-job-list-item .job__apply .button__success.button__radius:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${_t||""}\n        }\n        .ej-template-elegant .ej-job-list-elegant .ej-job-list-item-cat .job__apply .button.button__primary.radius-15 {\n            ${S?`background-color: ${S} !important;`:""}\n            ${x?`color: ${x} !important;`:""}\n            ${_t||""}\n            ${I||""}\n        }\n        .ej-template-elegant .ej-job-list-elegant .ej-job-list-item-cat .job__apply .button.button__primary.radius-15:hover {\n            ${T?`background-color: ${T} !important;`:""}\n            ${B?`color: ${B} !important;`:""}\n            ${_t||""}\n        }\n\t\n\t\t`),no=Ne(`\n\t\t\t${to}\n\t\t`),lo=Ne(`\n\t\t\t${oo}\n\t\t`);return(0,p.createElement)(p.Fragment,null,(0,p.createElement)(De,{attributes:t,setAttributes:o,desktopAllStyles:ao,tabAllStyles:no,mobileAllStyles:lo,blockName:a}))}const{BlockProps:Ie}=window.EJControls,Me=(0,r.withSelect)(((e,t)=>({companyInfo:e("easyjobs").getCompanyInfo()})))((e=>{const[t,o]=(0,_.useState)({jobs:{},categories:{},locations:{}}),[a,n]=(0,_.useState)(!1),{attributes:s,companyInfo:r,isSelected:i}=e,{orderBy:c,sortBy:m,activeOnly:b,noOfJob:j,cover:y}=s;(0,_.useEffect)((()=>{1===r?.length&&"api-error"===r[0]&&n(!0)}),[r]),(0,_.useEffect)((()=>{l()({url:(()=>{const e=new URLSearchParams({action:"easyjobs_get_jobs_for_block",blocks_nonce:EasyJobsBlocksJs.blocks_nonce,orderby:c,order:m,status:b,row:j});return EasyJobsBlocksJs.ajax_url+"?"+e.toString()})()}).then((e=>{"success"===e.status?o({jobs:e.data.jobs,categories:e.data.categories,locations:e.data.locations}):console.error("Fetch error:",e?.message||e?.status)})).catch((e=>console.error(e)))}),[c,m,b,j]);const d={...e,style:(0,p.createElement)(Pe,{...e})};return y.length?(0,p.createElement)("div",null,(0,p.createElement)("img",{src:y,alt:"job list",style:{maxWidth:"100%"}})):(0,p.createElement)(p.Fragment,null,i&&!a&&(0,p.createElement)(ke,{...e}),(0,p.createElement)(Ie.Edit,{...d},(0,p.createElement)(p.Fragment,null,a?(0,p.createElement)("p",{className:"elej-error-msg-editor"},"Please Connect your EasyJobs Account"):t.jobs&&t.jobs.data?.length?(0,p.createElement)(p.Fragment,null,"default"===r.selected_template&&(0,p.createElement)(T,{props:e,jobsData:t}),"classic"===r.selected_template&&(0,p.createElement)(D,{props:e,jobsData:t}),"elegant"===r.selected_template&&(0,p.createElement)(I,{props:e,jobsData:t})):0===t?.jobs?.data?.length?(0,p.createElement)("h3",null,"No jobs found."):(0,p.createElement)("p",null,"Loading jobs..."))))})),Ae=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"easyjobs/job-list","version":"0.1.0","title":"Job List","category":"easyjobs","description":"Display job listings & customize how they appear on your career page.","editorStyle":"ej-editor-style","supports":{"anchor":true,"align":["wide","full"]},"textdomain":"easyjobs"}');var Fe,Je,He;function Oe(){return Oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var a in o)({}).hasOwnProperty.call(o,a)&&(e[a]=o[a])}return e},Oe.apply(null,arguments)}(0,b.registerBlockType)(Ae,{icon:function(e){return p.createElement("svg",Oe({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),Fe||(Fe=p.createElement("path",{fill:"#fff",d:"M0 0h32v32H0z"})),Je||(Je=p.createElement("path",{fill:"#597DFC",d:"M24.828 24.428c.568 0 1.023-.455 1.023-1.023 0-.569-.455-1.023-1.023-1.023-.569 0-1.023.454-1.023 1.023 0 .568.454 1.023 1.023 1.023M21.589 22.438a.874.874 0 0 0-.853.853c0 .454.398.852.853.852a.874.874 0 0 0 .853-.852c-.057-.455-.398-.853-.853-.853M27.044 24.769a.39.39 0 0 0-.398.398c0 .227.171.398.398.398.228 0 .398-.17.398-.398a.39.39 0 0 0-.398-.398M23.236 26.02a.57.57 0 0 0 0-*********** 0 0 0-.568.569c.057.34.284.568.568.568M13.515 11.014c.113 0 .227.056.284.056a1.08 1.08 0 1 0 0-2.16 1.08 1.08 0 0 0-1.08 1.08c0 .398.227.796.625.967.057 0 .114.057.17.057"})),He||(He=p.createElement("path",{fill:"#597DFC",d:"M20.962 18.233c.796-1.25 1.25-2.786 1.25-4.377 0-3.979-2.842-7.332-6.65-8.128a9 9 0 0 0-1.762-.17c-4.547 0-8.241 3.694-8.241 8.298 0 3.865 2.614 7.162 6.195 8.071-.568-.284-4.547-3.012-4.49-8.298 0-3.41 2.898-6.196 6.309-6.31 3.694-.113 6.764 2.843 6.764 6.537a6.47 6.47 0 0 1-4.036 6.025c-.511.17-2.217.568-3.694-1.421-.967-1.421-.853-2.956-.796-3.41.057-.853.739-1.478 1.534-1.478h.17c.967.057 1.592.739 1.536 1.648-.057.739-.228 1.535-.569 2.217l-.057.057-.17.34c-.057.114-.057.228-.057.342a.524.524 0 0 0 .625.398c.17 0 .284-.114.341-.228.74-1.08 1.25-2.16 1.25-3.467 0-.796-.284-1.535-.795-2.046-.568-.569-1.307-.853-2.16-.853h-.17c-.455 0-.853.114-1.194.284 0 0-1.819.569-1.99 2.672-.17 1.194-.056 3.126 1.706 5.058.227.228.398.455.682.683.284.227.568.397.91.625.17.114.34.17.454.227.284.114.625.227.966.284.74.114 1.478 0 2.16-.284a6.7 6.7 0 0 0 1.535-.852l1.364 1.25c.34-.341.852-.511 1.364-.511.796 0 1.535.511 1.819 1.193.17-.455.568-.796 1.023-.966z"})))},attributes:ue,edit:Me,save:()=>null,example:{attributes:{cover:`${EasyJobsLocalize?.image_url}/block-preview/list.png`}}});const Re=({props:e})=>{const{companyDetails:t,attributes:o}=e,{companyName:a,websiteLinkText:n,coverImgUrl:l,logoImgUrl:s}=o;return(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"easyjobs-block-info"},(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper"},(0,p.createElement)("div",{className:"easyjobs-details"},!t?.remove_cover_photo&&t?.cover_photo&&(0,p.createElement)("div",{className:"ej-job-cover"},(0,p.createElement)("img",{src:l||t?.cover_photo[0],alt:t?.name})),(0,p.createElement)("div",{className:"ej-header"},(0,p.createElement)("div",{className:"ej-company-highlights"},(0,p.createElement)("div",{className:"ej-company-info"},(0,p.createElement)("div",{className:"logo"},(0,p.createElement)("img",{src:s||t?.logo,alt:"logo"})),(0,p.createElement)("div",{className:"info"},(0,p.createElement)("h2",{className:"name"},""===a?t?.name+" ":a+" ",t?.badge&&(0,p.createElement)("span",{className:"tooltip"},(0,p.createElement)("svg",{width:"25",viewBox:"0 0 44 43",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,p.createElement)("g",{"clip-path":"url(#clip0_101_41)"},(0,p.createElement)("path",{d:"M21.6843 3.60303C23.0779 3.60311 24.4237 4.11073 25.4701 5.03099L25.7461 5.29078L26.9966 6.54136C27.3398 6.88234 27.788 7.09759 28.2687 7.15232L28.5106 7.16665H30.3023C31.7664 7.16657 33.1752 7.72667 34.2396 8.7321C35.304 9.73753 35.9434 11.1121 36.0266 12.5739L36.0356 12.9V14.6917C36.0356 15.1754 36.2004 15.6466 36.4978 16.0229L36.6591 16.202L37.9079 17.4526C38.943 18.4818 39.5465 19.8671 39.5954 21.326C39.6444 22.7848 39.135 24.2074 38.1713 25.3037L37.9115 25.5796L36.6609 26.8302C36.3199 27.1734 36.1047 27.6216 36.0499 28.1023L36.0356 28.3442V30.1358C36.0357 31.6 35.4756 33.0087 34.4701 34.0731C33.4647 35.1375 32.0902 35.7769 30.6283 35.8602L30.3023 35.8692H28.5106C28.0275 35.8693 27.5586 36.0321 27.1794 36.3314L27.0002 36.4927L25.7496 37.7414C24.7204 38.7765 23.3352 39.3801 21.8763 39.429C20.4174 39.4779 18.9948 38.9686 17.8986 38.0048L17.6226 37.745L16.3721 36.4944C16.0288 36.1535 15.5807 35.9382 15.1 35.8835L14.8581 35.8692H13.0664C11.6022 35.8692 10.1935 35.3091 9.12912 34.3037C8.06472 33.2983 7.42533 31.9237 7.34205 30.4619L7.33309 30.1358V28.3442C7.33294 27.8611 7.1701 27.3921 6.87084 27.0129L6.70959 26.8338L5.4608 25.5832C4.42572 24.554 3.82219 23.1687 3.77325 21.7098C3.72431 20.251 4.23365 18.8284 5.19743 17.7321L5.45722 17.4562L6.7078 16.2056C7.04878 15.8624 7.26403 15.4142 7.31876 14.9335L7.33309 14.6917V12.9L7.34205 12.5739C7.42205 11.1682 8.01648 9.84116 9.01204 8.8456C10.0076 7.85004 11.3347 7.25561 12.7403 7.17561L13.0664 7.16665H14.8581C15.3412 7.16649 15.8101 7.00366 16.1893 6.7044L16.3685 6.54315L17.6191 5.29436C18.1518 4.75845 18.7852 4.33314 19.4829 4.04288C20.1805 3.75262 20.9287 3.60314 21.6843 3.60303ZM28.3081 16.6499C27.9721 16.3141 27.5165 16.1254 27.0414 16.1254C26.5663 16.1254 26.1107 16.3141 25.7747 16.6499L19.8748 22.5481L17.5581 20.2333L17.3897 20.0846C17.0296 19.8061 16.577 19.6752 16.1239 19.7184C15.6707 19.7615 15.251 19.9756 14.9499 20.317C14.6489 20.6584 14.489 21.1016 14.5029 21.5566C14.5168 22.0116 14.7034 22.4443 15.0247 22.7667L18.6081 26.35L18.7765 26.4987C19.1212 26.7661 19.5516 26.8986 19.9871 26.8712C20.4225 26.8438 20.833 26.6585 21.1415 26.35L28.3081 19.1834L28.4568 19.0149C28.7243 18.6702 28.8567 18.2398 28.8293 17.8043C28.8019 17.3689 28.6166 16.9585 28.3081 16.6499Z",fill:t?.badge.color})),(0,p.createElement)("defs",null,(0,p.createElement)("clipPath",{id:"clip0_101_41"},(0,p.createElement)("rect",{width:"43",height:"43",fill:"white",transform:"translate(0.166504)"})))),(0,p.createElement)("span",{className:"tooltiptext"},t?.badge?.label))),(t?.address?.city?.name||t?.address?.country?.name)&&(0,p.createElement)("p",{className:"location"},(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-map-maker"}),(0,p.createElement)("span",null,t?.address?.city?.name?t?.address?.city?.name+", ":"",t?.address?.country?.name?t?.address?.country?.name:"")))),t?.show_explore_company&&(0,p.createElement)("div",{className:"ej-header-tools"},(0,p.createElement)("a",{href:t?.website?t?.website:"#",className:"ej-btn ej-info-btn",target:"_blank"},""===n?"Explore company website":n))),(0,p.createElement)("div",{className:"ej-company-description",dangerouslySetInnerHTML:{__html:t.description}}))))))},qe=({props:e})=>{const{companyDetails:t,attributes:o}=e,{companyName:a,websiteLinkText:n,coverImgUrl:l,logoImgUrl:s}=o;return(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"easyjobs-block-info"},(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper ej-template-classic"},(0,p.createElement)("div",{className:"easyjobs-details"},(0,p.createElement)("div",{className:"pb100"},(0,p.createElement)("div",{className:"ej-container"},(0,p.createElement)("div",{className:"ej-row"},(0,p.createElement)("div",{className:"ej-col"},(0,p.createElement)("div",{className:"carrier__company"},(0,p.createElement)("div",{className:"ej-company-info"},(0,p.createElement)("div",{className:"logo"},(0,p.createElement)("img",{src:s||t?.logo,alt:"Logo"})),(0,p.createElement)("div",{className:"info"},(0,p.createElement)("h2",{className:"name"},""===a?t?.name+" ":a+" ",(0,p.createElement)("span",{className:"tooltip"},(0,p.createElement)("svg",{width:"25",viewBox:"0 0 44 43",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,p.createElement)("g",{"clip-path":"url(#clip0_101_41)"},(0,p.createElement)("path",{d:"M21.6843 3.60303C23.0779 3.60311 24.4237 4.11073 25.4701 5.03099L25.7461 5.29078L26.9966 6.54136C27.3398 6.88234 27.788 7.09759 28.2687 7.15232L28.5106 7.16665H30.3023C31.7664 7.16657 33.1752 7.72667 34.2396 8.7321C35.304 9.73753 35.9434 11.1121 36.0266 12.5739L36.0356 12.9V14.6917C36.0356 15.1754 36.2004 15.6466 36.4978 16.0229L36.6591 16.202L37.9079 17.4526C38.943 18.4818 39.5465 19.8671 39.5954 21.326C39.6444 22.7848 39.135 24.2074 38.1713 25.3037L37.9115 25.5796L36.6609 26.8302C36.3199 27.1734 36.1047 27.6216 36.0499 28.1023L36.0356 28.3442V30.1358C36.0357 31.6 35.4756 33.0087 34.4701 34.0731C33.4647 35.1375 32.0902 35.7769 30.6283 35.8602L30.3023 35.8692H28.5106C28.0275 35.8693 27.5586 36.0321 27.1794 36.3314L27.0002 36.4927L25.7496 37.7414C24.7204 38.7765 23.3352 39.3801 21.8763 39.429C20.4174 39.4779 18.9948 38.9686 17.8986 38.0048L17.6226 37.745L16.3721 36.4944C16.0288 36.1535 15.5807 35.9382 15.1 35.8835L14.8581 35.8692H13.0664C11.6022 35.8692 10.1935 35.3091 9.12912 34.3037C8.06472 33.2983 7.42533 31.9237 7.34205 30.4619L7.33309 30.1358V28.3442C7.33294 27.8611 7.1701 27.3921 6.87084 27.0129L6.70959 26.8338L5.4608 25.5832C4.42572 24.554 3.82219 23.1687 3.77325 21.7098C3.72431 20.251 4.23365 18.8284 5.19743 17.7321L5.45722 17.4562L6.7078 16.2056C7.04878 15.8624 7.26403 15.4142 7.31876 14.9335L7.33309 14.6917V12.9L7.34205 12.5739C7.42205 11.1682 8.01648 9.84116 9.01204 8.8456C10.0076 7.85004 11.3347 7.25561 12.7403 7.17561L13.0664 7.16665H14.8581C15.3412 7.16649 15.8101 7.00366 16.1893 6.7044L16.3685 6.54315L17.6191 5.29436C18.1518 4.75845 18.7852 4.33314 19.4829 4.04288C20.1805 3.75262 20.9287 3.60314 21.6843 3.60303ZM28.3081 16.6499C27.9721 16.3141 27.5165 16.1254 27.0414 16.1254C26.5663 16.1254 26.1107 16.3141 25.7747 16.6499L19.8748 22.5481L17.5581 20.2333L17.3897 20.0846C17.0296 19.8061 16.577 19.6752 16.1239 19.7184C15.6707 19.7615 15.251 19.9756 14.9499 20.317C14.6489 20.6584 14.489 21.1016 14.5029 21.5566C14.5168 22.0116 14.7034 22.4443 15.0247 22.7667L18.6081 26.35L18.7765 26.4987C19.1212 26.7661 19.5516 26.8986 19.9871 26.8712C20.4225 26.8438 20.833 26.6585 21.1415 26.35L28.3081 19.1834L28.4568 19.0149C28.7243 18.6702 28.8567 18.2398 28.8293 17.8043C28.8019 17.3689 28.6166 16.9585 28.3081 16.6499Z",fill:t?.badge?.color})),(0,p.createElement)("defs",null,(0,p.createElement)("clipPath",{id:"clip0_101_41"},(0,p.createElement)("rect",{width:"43",height:"43",fill:"white",transform:"translate(0.166504)"})))),(0,p.createElement)("span",{className:"tooltiptext"},t?.badge?.label))),(t?.address?.city?.name||t?.address?.country?.name)&&(0,p.createElement)("div",{className:"location"},(0,p.createElement)("span",{className:"label label__primary"},(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-map-maker"}),t?.address?.city?.name?t?.address?.city?.name+", ":"",t?.address?.country?.name?t?.address?.country?.name:"")))),t?.description&&(0,p.createElement)("div",{className:"ej-company-description",dangerouslySetInnerHTML:{__html:t.description}}),t?.show_explore_company&&(0,p.createElement)("a",{href:t?.website?t?.website:"#",target:"_blank",className:"button button__success button__radius"},""===n?"Explore company website":n)))))),!t?.remove_cover_photo&&t?.cover_photo&&(0,p.createElement)("div",{className:"ej-job-cover"},(0,p.createElement)("img",{src:l||t?.cover_photo[0],alt:"cover_photo"}))))))},Ue=({props:e})=>{const{companyDetails:t,attributes:o}=e,{companyName:a,websiteLinkText:n,coverImgUrl:l,logoImgUrl:s}=o;return(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"easyjobs-block-info"},(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper ej-template-elegant"},(0,p.createElement)("div",{className:"pt150 pb100"},(0,p.createElement)("div",{className:"ej-container"},(0,p.createElement)("div",{className:"ej-row"},(0,p.createElement)("div",{className:"ej-col"},(0,p.createElement)("div",{className:"about__company"},(0,p.createElement)("div",{className:"ej-company-info"},(0,p.createElement)("div",{className:"logo"},(0,p.createElement)("img",{src:s||t?.logo,alt:""})),(0,p.createElement)("div",{className:"info"},(0,p.createElement)("h4",{className:"name"},(0,p.createElement)("a",{href:t?.website?t?.website:"#",target:"_blank"},""===a?t?.name:a),t?.badge&&(0,p.createElement)("span",{className:"tooltip"},(0,p.createElement)("svg",{width:"25",viewBox:"0 0 44 43",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,p.createElement)("g",{"clip-path":"url(#clip0_101_41)"},(0,p.createElement)("path",{d:"M21.6843 3.60303C23.0779 3.60311 24.4237 4.11073 25.4701 5.03099L25.7461 5.29078L26.9966 6.54136C27.3398 6.88234 27.788 7.09759 28.2687 7.15232L28.5106 7.16665H30.3023C31.7664 7.16657 33.1752 7.72667 34.2396 8.7321C35.304 9.73753 35.9434 11.1121 36.0266 12.5739L36.0356 12.9V14.6917C36.0356 15.1754 36.2004 15.6466 36.4978 16.0229L36.6591 16.202L37.9079 17.4526C38.943 18.4818 39.5465 19.8671 39.5954 21.326C39.6444 22.7848 39.135 24.2074 38.1713 25.3037L37.9115 25.5796L36.6609 26.8302C36.3199 27.1734 36.1047 27.6216 36.0499 28.1023L36.0356 28.3442V30.1358C36.0357 31.6 35.4756 33.0087 34.4701 34.0731C33.4647 35.1375 32.0902 35.7769 30.6283 35.8602L30.3023 35.8692H28.5106C28.0275 35.8693 27.5586 36.0321 27.1794 36.3314L27.0002 36.4927L25.7496 37.7414C24.7204 38.7765 23.3352 39.3801 21.8763 39.429C20.4174 39.4779 18.9948 38.9686 17.8986 38.0048L17.6226 37.745L16.3721 36.4944C16.0288 36.1535 15.5807 35.9382 15.1 35.8835L14.8581 35.8692H13.0664C11.6022 35.8692 10.1935 35.3091 9.12912 34.3037C8.06472 33.2983 7.42533 31.9237 7.34205 30.4619L7.33309 30.1358V28.3442C7.33294 27.8611 7.1701 27.3921 6.87084 27.0129L6.70959 26.8338L5.4608 25.5832C4.42572 24.554 3.82219 23.1687 3.77325 21.7098C3.72431 20.251 4.23365 18.8284 5.19743 17.7321L5.45722 17.4562L6.7078 16.2056C7.04878 15.8624 7.26403 15.4142 7.31876 14.9335L7.33309 14.6917V12.9L7.34205 12.5739C7.42205 11.1682 8.01648 9.84116 9.01204 8.8456C10.0076 7.85004 11.3347 7.25561 12.7403 7.17561L13.0664 7.16665H14.8581C15.3412 7.16649 15.8101 7.00366 16.1893 6.7044L16.3685 6.54315L17.6191 5.29436C18.1518 4.75845 18.7852 4.33314 19.4829 4.04288C20.1805 3.75262 20.9287 3.60314 21.6843 3.60303ZM28.3081 16.6499C27.9721 16.3141 27.5165 16.1254 27.0414 16.1254C26.5663 16.1254 26.1107 16.3141 25.7747 16.6499L19.8748 22.5481L17.5581 20.2333L17.3897 20.0846C17.0296 19.8061 16.577 19.6752 16.1239 19.7184C15.6707 19.7615 15.251 19.9756 14.9499 20.317C14.6489 20.6584 14.489 21.1016 14.5029 21.5566C14.5168 22.0116 14.7034 22.4443 15.0247 22.7667L18.6081 26.35L18.7765 26.4987C19.1212 26.7661 19.5516 26.8986 19.9871 26.8712C20.4225 26.8438 20.833 26.6585 21.1415 26.35L28.3081 19.1834L28.4568 19.0149C28.7243 18.6702 28.8567 18.2398 28.8293 17.8043C28.8019 17.3689 28.6166 16.9585 28.3081 16.6499Z",fill:t.badge.color})),(0,p.createElement)("defs",null,(0,p.createElement)("clipPath",{id:"clip0_101_41"},(0,p.createElement)("rect",{width:"43",height:"43",fill:"white",transform:"translate(0.166504)"})))),(0,p.createElement)("span",{className:"tooltiptext"},t?.badge?.label))),(t?.address?.city?.name||t?.address?.country?.name)&&(0,p.createElement)("span",{className:"location"},(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-map-maker"}),t?.address?.city?.name?t?.address?.city?.name+", ":"",t?.address?.country?.name?t?.address?.country?.name:"")),t?.show_explore_company&&(0,p.createElement)("div",{className:"ej-header-tools"},(0,p.createElement)("a",{href:t?.website?t?.website:"#",className:"ej-btn ej-info-btn"},""===n?"Explore company website":n))),(0,p.createElement)("div",{className:"ej-company-description",dangerouslySetInnerHTML:{__html:t.description}}),!t?.remove_cover_photo&&t?.cover_photo&&(0,p.createElement)("div",{className:"about__company__bottom"},(0,p.createElement)("div",{className:"ej-job-cover"},(0,p.createElement)("img",{src:l||t?.cover_photo[0],alt:"cover_photo"})))))))))))},Ve=[{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignleft"})),value:"left"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-aligncenter"})),value:"center"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignright"})),value:"right"}],ze=[{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignleft"})),value:"left"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-aligncenter"})),value:"center"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignright"})),value:"right"},{label:(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-justify"})),value:"justify"}],We=((0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignleft"})),(0,y.__)((0,p.createElement)(d.Dashicon,{icon:"editor-alignright"})),(0,y.__)("Fixed","easyjobs"),"infoWidth"),Ge="infoMargin",Ze="infoPadding",Ye="infoBg",Qe="websiteLinkBtnPadding",Ke="websiteLinkBtnBg",Xe="infoBoxShw",et="websiteLinkBtnBDRShw",tt="company_text",ot="location_text",at="website_text",nt="description_text",{softMinifyCssStrings:lt,generateDimensionsControlStyles:st,generateBorderShadowStyles:rt,generateTypographyStyles:it,generateBackgroundControlStyles:ct,generateResponsiveRangeStyles:mt,StyleComponent:bt}=window.EJControls;function pt(e){const{attributes:t,setAttributes:o,name:a}=e,{companyNameColor:n,locationNameColor:l,websiteLinkBtnColor:s,websiteLinkBtnColorHvr:r,descriptionColor:i,infoAlign:c,descriptionAlign:m}=t,{rangeStylesDesktop:b,rangeStylesTab:j,rangeStylesMobile:y}=mt({controlName:We,property:"width",attributes:t}),{dimensionStylesDesktop:d,dimensionStylesTab:g,dimensionStylesMobile:u}=st({controlName:Qe,styleFor:"padding",attributes:t}),{dimensionStylesDesktop:_,dimensionStylesTab:h,dimensionStylesMobile:f}=st({controlName:Ge,styleFor:"margin",attributes:t}),{dimensionStylesDesktop:E,dimensionStylesTab:C,dimensionStylesMobile:v}=st({controlName:Ze,styleFor:"padding",attributes:t}),{backgroundStylesDesktop:$,hoverBackgroundStylesDesktop:w,backgroundStylesTab:k,hoverBackgroundStylesTab:N,backgroundStylesMobile:x,hoverBackgroundStylesMobile:S,bgTransitionStyle:B}=ct({attributes:t,controlName:Ke}),{backgroundStylesDesktop:T,hoverBackgroundStylesDesktop:L,backgroundStylesTab:D,hoverBackgroundStylesTab:P,backgroundStylesMobile:I,hoverBackgroundStylesMobile:M,bgTransitionStyle:A}=ct({attributes:t,controlName:Ye}),{styesDesktop:F,styesTab:J,styesMobile:H,stylesHoverDesktop:O,stylesHoverTab:R,stylesHoverMobile:q,transitionStyle:U}=rt({controlName:et,attributes:t}),{styesDesktop:V,styesTab:z,styesMobile:W,stylesHoverDesktop:G,stylesHoverTab:Z,stylesHoverMobile:Y,transitionStyle:Q}=rt({controlName:Xe,attributes:t}),{typoStylesDesktop:K,typoStylesTab:X,typoStylesMobile:ee}=it({attributes:t,prefixConstant:tt}),{typoStylesDesktop:te,typoStylesTab:oe,typoStylesMobile:ae}=it({attributes:t,prefixConstant:ot}),{typoStylesDesktop:ne,typoStylesTab:le,typoStylesMobile:se}=it({attributes:t,prefixConstant:at}),{typoStylesDesktop:re,typoStylesTab:ie,typoStylesMobile:ce}=it({attributes:t,prefixConstant:nt}),me=`\n\t\t.easyjobs-block-info {\n            ${"center"===c?"display: flex; justify-content: center;":""}\n            ${"left"===c?"display: flex; justify-content: flex-start;":""}\n            ${"right"===c?"display: flex; justify-content: flex-end;":""}\n            ${z}\n        }\n        .easyjobs-block-info:hover {\n            ${Z}\n        } \n        .easyjobs-block-info .easyjobs-shortcode-wrapper {\n            ${D};\n            ${A};\n            ${j};\n            ${h}\n            ${C}\n        }\n        .easyjobs-block-info .easyjobs-shortcode-wrapper:hover {\n            ${P}\n            ${A}\n        }   \n        .ej-company-info .info .name,\n        .ej-template-elegant .about__company .ej-company-info .info .name a {\n            ${n?`color: ${n};`:""}\n            ${X||""}\n        }\n        .ej-company-info .info .location,\n        .ej-template-elegant .about__company .ej-company-info .info .location,\n        .easyjobs-shortcode-wrapper.ej-template-classic .label__primary {\n            ${l?`color: ${l};`:""}\n            ${oe||""}\n        }\n        .ej-header .ej-company-highlights .ej-header-tools .ej-info-btn,\n        .ej-template-elegant.ej-template-elegant .ej-header-tools .ej-info-btn,\n        .easyjobs-shortcode-wrapper.ej-template-classic .carrier__company .button {\n            ${s?`color: ${s};`:""}\n            ${le||""}\n            ${k}\n            ${J}\n            ${g}\n        }\n        .ej-header .ej-company-highlights .ej-header-tools .ej-info-btn:hover,\n        .easyjobs-shortcode-wrapper.ej-template-elegant.ej-template-elegant .ej-header-tools .ej-btn.ej-info-btn:hover,\n        .easyjobs-shortcode-wrapper.ej-template-classic .carrier__company .button:hover {\n            ${r?`color: ${r};`:""}\n            ${N}\n            ${R}\n        }\n        .ej-company-description, .ej-company-description h2, .ej-company-description p, .ej-company-description ul li, .ej-company-description ol li, .ej-company-description blockquote,\n        .easyjobs-shortcode-wrapper.ej-template-classic .ej-company-description p,\n        .ej-template-elegant .ej-company-description p {\n            ${m?`text-align: ${m};`:""}\n            ${i?`color: ${i};`:""}\n            ${ie||""}\n        }\n\t`,be=`\n\t\t.easyjobs-block-info {\n            ${"center"===c?"display: flex; justify-content: center;":""}\n            ${"left"===c?"display: flex; justify-content: flex-start;":""}\n            ${"right"===c?"display: flex; justify-content: flex-end;":""}\n            ${W}\n        }\n        .easyjobs-block-info:hover {\n            ${Y}\n        } \n        .easyjobs-block-info .easyjobs-shortcode-wrapper {\n            ${I};\n            ${A};\n            ${y};\n            ${f}\n            ${v}\n        }\n        .easyjobs-block-info .easyjobs-shortcode-wrapper:hover {\n            ${M}\n            ${A}\n        }   \n        .ej-company-info .info .name,\n        .ej-template-elegant .about__company .ej-company-info .info .name a {\n            ${n?`color: ${n};`:""}\n            ${ee||""}\n        }\n        .ej-company-info .info .location,\n        .ej-template-elegant .about__company .ej-company-info .info .location,\n        .easyjobs-shortcode-wrapper.ej-template-classic .label__primary {\n            ${l?`color: ${l};`:""}\n            ${ae||""}\n        }\n        .ej-header .ej-company-highlights .ej-header-tools .ej-info-btn,\n        .ej-template-elegant.ej-template-elegant .ej-header-tools .ej-info-btn,\n        .easyjobs-shortcode-wrapper.ej-template-classic .carrier__company .button {\n            ${s?`color: ${s};`:""}\n            ${se||""}\n            ${x}\n            ${H}\n            ${u}\n        }\n        .ej-header .ej-company-highlights .ej-header-tools .ej-info-btn:hover,\n        .easyjobs-shortcode-wrapper.ej-template-elegant.ej-template-elegant .ej-header-tools .ej-btn.ej-info-btn:hover,\n        .easyjobs-shortcode-wrapper.ej-template-classic .carrier__company .button:hover {\n            ${r?`color: ${r};`:""}\n            ${S}\n            ${q}\n        }\n        .ej-company-description, .ej-company-description h2, .ej-company-description p, .ej-company-description ul li, .ej-company-description ol li, .ej-company-description blockquote,\n        .easyjobs-shortcode-wrapper.ej-template-classic .ej-company-description p,\n        .ej-template-elegant .ej-company-description p {\n            ${m?`text-align: ${m};`:""}\n            ${i?`color: ${i};`:""}\n            ${ce||""}\n        }\n\t`,pe=lt(`\n\t\t\t\n        .easyjobs-block-info {\n            ${"center"===c?"display: flex; justify-content: center;":""}\n            ${"left"===c?"display: flex; justify-content: flex-start;":""}\n            ${"right"===c?"display: flex; justify-content: flex-end;":""}\n            ${V}\n        }\n        .easyjobs-block-info:hover {\n            ${G}\n        } \n        .easyjobs-block-info .easyjobs-shortcode-wrapper {\n            ${T};\n            ${A};\n            ${b};\n            ${_}\n            ${E}\n        }\n        .easyjobs-block-info .easyjobs-shortcode-wrapper:hover {\n            ${L}\n            ${A}\n        }   \n        .ej-company-info .info .name,\n        .ej-template-elegant .about__company .ej-company-info .info .name a {\n            ${n?`color: ${n};`:""}\n            ${K||""}\n        }\n        .ej-company-info .info .location,\n        .ej-template-elegant .about__company .ej-company-info .info .location,\n        .easyjobs-shortcode-wrapper.ej-template-classic .label__primary {\n            ${l?`color: ${l};`:""}\n            ${te||""}\n        }\n        .ej-header .ej-company-highlights .ej-header-tools .ej-info-btn,\n        .ej-template-elegant.ej-template-elegant .ej-header-tools .ej-info-btn,\n        .easyjobs-shortcode-wrapper.ej-template-classic .carrier__company .button {\n            ${s?`color: ${s};`:""}\n            ${ne||""}\n            ${$}\n            ${F}\n            ${d}\n        }\n        .ej-header .ej-company-highlights .ej-header-tools .ej-info-btn:hover,\n        .easyjobs-shortcode-wrapper.ej-template-elegant.ej-template-elegant .ej-header-tools .ej-btn.ej-info-btn:hover,\n        .easyjobs-shortcode-wrapper.ej-template-classic .carrier__company .button:hover {\n            ${r?`color: ${r};`:""}\n            ${w}\n            ${O}\n        }\n        .ej-company-description, .ej-company-description h2, .ej-company-description p, .ej-company-description ul li, .ej-company-description ol li, .ej-company-description blockquote,\n        .easyjobs-shortcode-wrapper.ej-template-classic .ej-company-description p,\n        .ej-template-elegant .ej-company-description p {\n            ${m?`text-align: ${m};`:""}\n            ${i?`color: ${i};`:""}\n            ${re||""}\n        }\n\t\n\t\t`),je=lt(`\n\t\t\t${me}\n\t\t`),ye=lt(`\n\t\t\t${be}\n\t\t`);return(0,p.createElement)(p.Fragment,null,(0,p.createElement)(bt,{attributes:t,setAttributes:o,desktopAllStyles:pe,tabAllStyles:je,mobileAllStyles:ye,blockName:a}))}const{generateResponsiveRangeAttributes:jt,generateDimensionsAttributes:yt,generateTypographyAttributes:dt,generateBorderShadowAttributes:gt,generateBackgroundAttributes:ut}=window.EJControls,_t={resOption:{type:"string",default:"Desktop"},blockId:{type:"string"},blockMeta:{type:"object"},changeCoverImage:{type:"boolean",default:!1},changeLogoImage:{type:"boolean",default:!1},companyName:{type:"string",default:(0,y.__)("","easyjobs")},websiteLinkText:{type:"string",default:(0,y.__)("Explore company website","easyjobs")},coverImgUrl:{type:"string",default:""},coverImgId:{type:"string"},coverImgAlt:{type:"string"},logoImgUrl:{type:"string",default:""},logoImgId:{type:"string"},logoImgAlt:{type:"string"},blockAlign:{type:"string",default:"center"},descriptionAlign:{type:"string",default:"left"},companyNameColor:{type:"string",default:""},locationNameColor:{type:"string",default:""},websiteLinkBtnColor:{type:"string",default:""},websiteLinkBtnColorHvr:{type:"string",default:""},descriptionColor:{type:"string",default:""},infoAlign:{type:"string",default:"center"},cover:{type:"string",default:""},...dt(Object.values(o)),...jt("blockWidth",{defaultRange:100,defaultUnit:"%"}),...jt(We,{defaultRange:100,defaultUnit:"%"}),...yt("blockMargin",{top:"",right:"",bottom:"",left:"",isLinked:!0}),...yt(Ge,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...yt(Ze,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...yt("blockPadding",{top:"",right:"",bottom:"",left:"",isLinked:!0}),...yt(Qe,{top:"",right:"",bottom:"",left:"",isLinked:!0}),...ut(Ke,{noOverlay:!0,noMainBgi:!0,defaultFillColor:"#2fc1e1",defaultHovFillColor:"#1fb6d7"}),...gt("blockBoxShw"),...gt(Xe),...gt(et),...ut(Ye,{noOverlay:!0,noMainBgi:!0,defaultFillColor:"#f9f9f9",defaultHovFillColor:"#f9f9f9"})},{ColorControl:ht,DynamicInputControl:ft,ImageAvatar:Et,BackgroundControl:Ct,ResponsiveRangeController:vt,ResponsiveDimensionsControl:$t,TypographyDropdown:wt,BorderShadowControl:kt}=window.EJControls,Nt=({attributes:e,setAttributes:t})=>{const{resOption:o,changeCoverImage:a,changeLogoImage:n,companyName:l,websiteLinkText:s,coverImgUrl:r,coverImgId:i,logoImgUrl:c,logoImgId:m,infoAlign:b,descriptionAlign:u,companyNameColor:_,locationNameColor:h,websiteLinkBtnColor:f,websiteLinkBtnColorHvr:E,descriptionColor:C}=e,v={setAttributes:t,resOption:o,attributes:e,objAttributes:_t};return(0,p.createElement)(j.InspectorControls,{key:"controls"},(0,p.createElement)("div",{className:"eb-panel-control"},(0,p.createElement)(d.TabPanel,{className:"eb-parent-tab-panel",activeClass:"active-tab",tabs:[{name:"content",title:(0,y.__)("Content","easyjobs"),className:"eb-tab general"},{name:"styles",title:(0,y.__)("Style","easyjobs"),className:"eb-tab styles"},{name:"advance",title:(0,y.__)("Advanced","easyjobs"),className:"eb-tab advance"}]},(e=>(0,p.createElement)("div",{className:"eb-tab-controls "+e.name},"content"===e.name&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.PanelBody,{title:(0,y.__)("EasyJobs","easyjobs")},(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Change Cover Image","easyjobs"),checked:a,onChange:()=>t({changeCoverImage:!a,coverImgUrl:null})}),!r&&a&&(0,p.createElement)(j.MediaUpload,{onSelect:({id:e,url:o,alt:a})=>t({coverImgUrl:o,coverImgId:e,coverImgAlt:a}),type:"image",value:i,render:({open:e})=>(0,p.createElement)(d.Button,{className:"eb-background-control-inspector-panel-img-btn components-button",label:(0,y.__)("Upload Image","easyjobs"),icon:"format-image",onClick:e})}),r&&a&&(0,p.createElement)(Et,{imageUrl:r,onDeleteImage:()=>t({coverImgUrl:null})}),(0,p.createElement)(d.ToggleControl,{label:(0,y.__)("Change Logo","easyjobs"),checked:n,onChange:()=>t({changeLogoImage:!n,logoImgUrl:null})}),!c&&n&&(0,p.createElement)(j.MediaUpload,{onSelect:({id:e,url:o,alt:a})=>t({logoImgUrl:o,logoImgId:e,logoImgAlt:a}),type:"image",value:m,render:({open:e})=>(0,p.createElement)(d.Button,{className:"eb-background-control-inspector-panel-img-btn components-button",label:(0,y.__)("Upload Image","easyjobs"),icon:"format-image",onClick:e})}),c&&n&&(0,p.createElement)(Et,{imageUrl:c,onDeleteImage:()=>t({logoImgUrl:null})})),(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Text change","easyjobs"),initialOpen:!1},(0,p.createElement)(ft,{label:"Company Name",attrName:"companyName",inputValue:l,setAttributes:t,onChange:e=>t({companyName:e})}),(0,p.createElement)(ft,{label:"Website Link Text",attrName:"websiteLinkText",inputValue:s,setAttributes:t,onChange:e=>t({websiteLinkText:e})}))),"styles"===e.name&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.PanelBody,{title:(0,y.__)("General","easyjobs"),initialOpen:!1},(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Background","easyjobs"))),(0,p.createElement)(Ct,{controlName:Ye,resRequiredProps:v,noOverlay:!0,noMainBgi:!0}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,{label:(0,y.__)("Alignment","easyjobs")},(0,p.createElement)(d.ButtonGroup,{id:"eb-button-group-alignment"},Ve.map(((e,o)=>(0,p.createElement)(d.Button,{key:o,isPrimary:b===e.value,isSecondary:b!==e.value,onClick:()=>t({infoAlign:e.value})},e.label))))),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(vt,{baseLabel:(0,y.__)("Width","easyjobs"),controlName:We,resRequiredProps:v,min:0,max:100,step:1}),(0,p.createElement)($t,{resRequiredProps:v,controlName:Ge,baseLabel:(0,y.__)("Margin","easyjobs")}),(0,p.createElement)($t,{resRequiredProps:v,controlName:Ze,baseLabel:(0,y.__)("Padding","easyjobs")}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(kt,{controlName:Xe,resRequiredProps:v,noBorder:!0})),(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Company Info","easyjobs"),initialOpen:!1},(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Company Name","easyjobs"))),(0,p.createElement)(ht,{label:(0,y.__)("Color","easyjobs"),color:_,onChange:e=>t({companyNameColor:e})}),(0,p.createElement)(wt,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:tt,resRequiredProps:v}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Location","easyjobs"))),(0,p.createElement)(ht,{label:(0,y.__)("Color","easyjobs"),color:h,onChange:e=>t({locationNameColor:e})}),(0,p.createElement)(wt,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:ot,resRequiredProps:v}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Website Link Button","easyjobs"))),(0,p.createElement)(ht,{label:(0,y.__)("Color","easyjobs"),color:f,onChange:e=>t({websiteLinkBtnColor:e})}),(0,p.createElement)(ht,{label:(0,y.__)("Hover Color","easyjobs"),color:E,onChange:e=>t({websiteLinkBtnColorHvr:e})}),(0,p.createElement)(wt,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:at,resRequiredProps:v}),(0,p.createElement)(Ct,{controlName:Ke,resRequiredProps:v,noOverlay:!0,noMainBgi:!0}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(kt,{controlName:et,resRequiredProps:v}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)($t,{resRequiredProps:v,controlName:Qe,baseLabel:(0,y.__)("Padding","easyjobs")}),(0,p.createElement)(d.__experimentalDivider,null),(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Description","easyjobs"))),(0,p.createElement)(d.BaseControl,{label:(0,y.__)("Alignment","easyjobs")},(0,p.createElement)(d.ButtonGroup,{id:"eb-button-group-alignment"},ze.map(((e,o)=>(0,p.createElement)(d.Button,{key:o,isPrimary:u===e.value,isSecondary:u!==e.value,onClick:()=>t({descriptionAlign:e.value})},e.label))))),(0,p.createElement)(ht,{label:(0,y.__)("Color","easyjobs"),color:C,onChange:e=>t({descriptionColor:e})}),(0,p.createElement)(wt,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:nt,resRequiredProps:v}),(0,p.createElement)(d.__experimentalDivider,null))))))),(0,p.createElement)(g,{link:"https://easy.jobs/docs/design-company-profile-in-gutenberg-with-easy-jobs/"}))},{BlockProps:xt}=window.EJControls,St=(0,r.withSelect)(((e,t)=>({companyInfo:e("easyjobs").getCompanyInfo(),companyDetails:e("easyjobs").getCompanyDetails()})))((e=>{const{companyInfo:t,isSelected:o,attributes:a}=e,{cover:n}=a,[l,s]=(0,_.useState)(!1);(0,_.useEffect)((()=>{1===t?.length&&"api-error"===t[0]&&s(!0)}),[t]);const r={...e,style:(0,p.createElement)(pt,{...e})};return n.length?(0,p.createElement)("div",null,(0,p.createElement)("img",{src:n,alt:"job header",style:{maxWidth:"100%"}})):(0,p.createElement)(p.Fragment,null,o&&!l&&(0,p.createElement)(Nt,{...e}),(0,p.createElement)(xt.Edit,{...r},(0,p.createElement)(p.Fragment,null,l?(0,p.createElement)("p",{className:"elej-error-msg-editor"},"Please Connect your EasyJobs Account"):t?(0,p.createElement)(p.Fragment,null,"default"===t.selected_template&&(0,p.createElement)(Re,{props:e}),"classic"===t.selected_template&&(0,p.createElement)(qe,{props:e}),"elegant"===t.selected_template&&(0,p.createElement)(Ue,{props:e})):(0,p.createElement)("p",null,"Loading header block..."))))})),Bt=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"easyjobs/job-header","version":"0.1.0","title":"Company Profile","category":"easyjobs","description":"Introduce your company with essential information and customize as necessary.","editorStyle":"ej-editor-style","supports":{"anchor":true,"align":["wide","full"]},"textdomain":"easyjobs"}');var Tt,Lt,Dt;function Pt(){return Pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var a in o)({}).hasOwnProperty.call(o,a)&&(e[a]=o[a])}return e},Pt.apply(null,arguments)}(0,b.registerBlockType)(Bt,{icon:function(e){return p.createElement("svg",Pt({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),Tt||(Tt=p.createElement("path",{fill:"#fff",d:"M0 0h32v32H0z"})),Lt||(Lt=p.createElement("path",{fill:"#597DFC",d:"M24.828 24.428c.568 0 1.023-.455 1.023-1.023 0-.569-.455-1.023-1.023-1.023-.569 0-1.023.454-1.023 1.023 0 .568.454 1.023 1.023 1.023M21.589 22.438a.874.874 0 0 0-.853.853c0 .454.398.852.853.852a.874.874 0 0 0 .853-.852c-.057-.455-.398-.853-.853-.853M27.044 24.769a.39.39 0 0 0-.398.398c0 .227.171.398.398.398.228 0 .398-.17.398-.398a.39.39 0 0 0-.398-.398M23.236 26.02a.57.57 0 0 0 0-*********** 0 0 0-.568.569c.057.34.284.568.568.568M13.515 11.014c.113 0 .227.056.284.056a1.08 1.08 0 1 0 0-2.16 1.08 1.08 0 0 0-1.08 1.08c0 .398.227.796.625.967.057 0 .114.057.17.057"})),Dt||(Dt=p.createElement("path",{fill:"#597DFC",d:"M20.962 18.233c.796-1.25 1.25-2.786 1.25-4.377 0-3.979-2.842-7.332-6.65-8.128a9 9 0 0 0-1.762-.17c-4.547 0-8.241 3.694-8.241 8.298 0 3.865 2.614 7.162 6.195 8.071-.568-.284-4.547-3.012-4.49-8.298 0-3.41 2.898-6.196 6.309-6.31 3.694-.113 6.764 2.843 6.764 6.537a6.47 6.47 0 0 1-4.036 6.025c-.511.17-2.217.568-3.694-1.421-.967-1.421-.853-2.956-.796-3.41.057-.853.739-1.478 1.534-1.478h.17c.967.057 1.592.739 1.536 1.648-.057.739-.228 1.535-.569 2.217l-.057.057-.17.34c-.057.114-.057.228-.057.342a.524.524 0 0 0 .625.398c.17 0 .284-.114.341-.228.74-1.08 1.25-2.16 1.25-3.467 0-.796-.284-1.535-.795-2.046-.568-.569-1.307-.853-2.16-.853h-.17c-.455 0-.853.114-1.194.284 0 0-1.819.569-1.99 2.672-.17 1.194-.056 3.126 1.706 5.058.227.228.398.455.682.683.284.227.568.397.91.625.17.114.34.17.454.227.284.114.625.227.966.284.74.114 1.478 0 2.16-.284a6.7 6.7 0 0 0 1.535-.852l1.364 1.25c.34-.341.852-.511 1.364-.511.796 0 1.535.511 1.819 1.193.17-.455.568-.796 1.023-.966z"})))},attributes:_t,edit:St,save:()=>null,example:{attributes:{cover:`${EasyJobsLocalize?.image_url}/block-preview/header.png`}}});const It=({props:e})=>{const{companyDetails:t,attributes:o}=e,{lifeAtTitle:a}=o;return(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper ej-template-default",id:"easyjobs-list"},(0,p.createElement)("div",{className:"easyjobs-details"},(0,p.createElement)("div",{className:"ej-job-body"},t?.show_life&&t?.showcase_photo?.length>0?(0,p.createElement)("div",{className:"ej-section"},(""!==a||t?.name)&&(0,p.createElement)("div",{className:"ej-section-title"},(0,p.createElement)("span",{className:"ej-section-title-icon"},(0,p.createElement)("i",{className:"easyjobs-icon easyjobs-briefcase"})),(0,p.createElement)("span",{className:"ej-section-title-text"},""!==a?a:`Life at ${t?.name}`)),(0,p.createElement)("div",{className:"ej-section-content"},(0,p.createElement)("div",{className:"ej-company-showcase"},(0,p.createElement)("div",{className:"ej-showcase-inner"},(0,p.createElement)("div",{className:"ej-showcase-left"},(0,p.createElement)("div",{className:"ej-showcase-image"},(0,p.createElement)("div",{className:"ej-image"},(0,p.createElement)("img",{src:t?.showcase_photo[0],alt:t?.name})))),t?.showcase_photo?.length>1&&(0,p.createElement)("div",{className:"ej-showcase-right"},t?.showcase_photo?.map(((e,o)=>{if(0!=o)return(0,p.createElement)("div",{className:"ej-showcase-image"},(0,p.createElement)("div",{className:"ej-image"},(0,p.createElement)("img",{src:e,alt:t?.name})))}))))))):(0,p.createElement)("p",{className:"elej-error-msg-editor"},"To use the Company Gallery, navigate to ",(0,p.createElement)("strong",null,"Settings > Photos & Colors,"),' and enable the "Show on company page" option.')))))},Mt=({props:e})=>{const{companyDetails:t,attributes:o}=e,{lifeAtTitle:a}=o;return jQuery(document).ready((function(e){e(".office__gallery__slider").owlCarousel({center:!0,loop:!0,margin:30,nav:!1,dots:!1,responsive:{0:{items:1},575:{items:3},992:{items:4}}})})),(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper ej-template-classic"},(0,p.createElement)("div",{className:"easyjobs-details"},t?.show_life&&t?.showcase_photo?.length>0?(0,p.createElement)("div",{className:"ej-section ej-company-showcase-classic"},(0,p.createElement)("div",{className:"section__header section__header--text-center",id:"open_job_position"},(0,p.createElement)("div",{className:"ej-section-title"},(0,p.createElement)("h2",{className:"ej-section-title-text"},""!==a?a:`Life at ${t?.name}`))),(0,p.createElement)("div",{className:"ej-section-content"},(0,p.createElement)("div",{className:"office__gallery__slider ej-company-showcase owl-carousel"},t?.showcase_photo?.map(((e,o)=>(0,p.createElement)("div",{className:"item"},(0,p.createElement)("img",{src:e,alt:t?.name}))))))):(0,p.createElement)("p",{className:"elej-error-msg-editor"},"To use the Company Gallery, navigate to ",(0,p.createElement)("strong",null,"Settings > Photos & Colors,"),' and enable the "Show on company page" option.'))))},At=({props:e})=>{const{companyDetails:t,attributes:o}=e,{lifeAtTitle:a}=o;return(0,p.createElement)(p.Fragment,null,(0,p.createElement)("div",{className:"easyjobs-shortcode-wrapper ej-template-elegant"},(0,p.createElement)("div",{className:"pt150 pb100"},t?.show_life&&t?.showcase_photo?.length>0?(0,p.createElement)("div",{className:"mt60"},(0,p.createElement)("div",{className:"ej-container"},(0,p.createElement)("div",{className:"ej-row"},(0,p.createElement)("div",{className:"ej-col"},(0,p.createElement)("div",{className:"section__header"},(0,p.createElement)("h2",null,""!==a?a:`Life at ${t?.name}`))))),(0,p.createElement)("div",{className:"image__gallery"},t?.showcase_photo?.map(((e,o)=>(0,p.createElement)("div",{className:"item"},(0,p.createElement)("img",{src:e,alt:t?.name})))))):(0,p.createElement)("p",{className:"elej-error-msg-editor"},"To use the Company Gallery, navigate to ",(0,p.createElement)("strong",null,"Settings > Photos & Colors,"),' and enable the "Show on company page" option.'))))},Ft="gallery_title",{softMinifyCssStrings:Jt,generateTypographyStyles:Ht,StyleComponent:Ot}=window.EJControls;function Rt(e){const{attributes:t,setAttributes:o,name:a}=e,{galleryTitleColor:n}=t,{typoStylesDesktop:l,typoStylesTab:s,typoStylesMobile:r}=Ht({attributes:t,prefixConstant:Ft}),i=`\n\t\t.easyjobs-shortcode-wrapper .ej-section-title .ej-section-title-text,\n        .easyjobs-shortcode-wrapper .ej-section-title h2.ej-section-title-text,\n        .easyjobs-shortcode-wrapper.ej-template-elegant .section__header h2 {\n            ${s||""}\n        }\n\t`,c=`\n\t\t.easyjobs-shortcode-wrapper .ej-section-title .ej-section-title-text,\n        .easyjobs-shortcode-wrapper .ej-section-title h2.ej-section-title-text,\n        .easyjobs-shortcode-wrapper.ej-template-elegant .section__header h2 {\n            ${r||""}\n        }\n\t`,m=Jt(`\n\t\t\t\n        .easyjobs-shortcode-wrapper .ej-section-title .ej-section-title-text,\n        .easyjobs-shortcode-wrapper .ej-section-title h2.ej-section-title-text,\n        .easyjobs-shortcode-wrapper.ej-template-elegant .section__header h2 {\n            ${n?`color: ${n};`:""}\n            ${l||""}\n        }\n\t\n\t\t`),b=Jt(`\n\t\t\t${i}\n\t\t`),j=Jt(`\n\t\t\t${c}\n\t\t`);return(0,p.createElement)(p.Fragment,null,(0,p.createElement)(Ot,{attributes:t,setAttributes:o,desktopAllStyles:m,tabAllStyles:b,mobileAllStyles:j,blockName:a}))}const{generateTypographyAttributes:qt}=window.EJControls,Ut={resOption:{type:"string",default:(0,y.__)("Desktop","easyjobs")},blockId:{type:"string"},blockMeta:{type:"object"},lifeAtTitle:{type:"string",default:(0,y.__)("Life at","easyjobs")},galleryTitleColor:{type:"string",default:""},cover:{type:"string",default:""},...qt(Object.values(a))},{ColorControl:Vt,DynamicInputControl:zt,TypographyDropdown:Wt}=window.EJControls,Gt=({attributes:e,setAttributes:t,companyDetails:o})=>{const{resOption:a,lifeAtTitle:n,galleryTitleColor:l}=e,s={setAttributes:t,resOption:a,attributes:e,objAttributes:Ut};return(0,p.createElement)(j.InspectorControls,{key:"controls"},(0,p.createElement)("div",{className:"eb-panel-control"},(0,p.createElement)(d.TabPanel,{className:"eb-parent-tab-panel",activeClass:"active-tab",tabs:[{name:"content",title:(0,y.__)("Content","easyjobs"),className:"eb-tab general"},{name:"styles",title:(0,y.__)("Style","easyjobs"),className:"eb-tab styles"},{name:"advance",title:(0,y.__)("Advanced","easyjobs"),className:"eb-tab advance"}]},(e=>(0,p.createElement)("div",{className:"eb-tab-controls "+e.name},"content"===e.name&&(0,p.createElement)(p.Fragment,null,o?.show_life?(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Text change","easyjobs"),initialOpen:!0},(0,p.createElement)(zt,{label:"Gallery Section Title",attrName:"lifeAtTitle",inputValue:n,setAttributes:t,onChange:e=>t({lifeAtTitle:e})})):(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Company Gallery","easyjobs"),initialOpen:!0},(0,p.createElement)(d.Card,null,(0,p.createElement)(d.CardBody,null,(0,p.createElement)("p",null,'Please make sure to enable the "Show on Company Page" from',(0,p.createElement)(d.ExternalLink,{href:`${EasyJobsLocalize.ej_admin_url}admin.php?page=easyjobs-settings`},(0,y.__)(" here."))))))),"styles"===e.name&&(0,p.createElement)(p.Fragment,null,(0,p.createElement)(d.PanelBody,{title:(0,y.__)("Gallery","easyjobs"),initialOpen:!0},(0,p.createElement)(d.BaseControl,null,(0,p.createElement)("h3",{className:"eb-control-title"},(0,y.__)("Gallery Title","easyjobs"))),(0,p.createElement)(Vt,{label:(0,y.__)("Color","easyjobs"),color:l,onChange:e=>t({galleryTitleColor:e})}),(0,p.createElement)(Wt,{baseLabel:(0,y.__)("Typography","easyjobs"),typographyPrefixConstant:Ft,resRequiredProps:s}))))))),(0,p.createElement)(g,{link:"https://easy.jobs/docs/showcase-company-gallery-using-gutenberg/"}))},{BlockProps:Zt}=window.EJControls,Yt=(0,r.withSelect)(((e,t)=>({companyInfo:e("easyjobs").getCompanyInfo(),companyDetails:e("easyjobs").getCompanyDetails()})))((e=>{const{companyInfo:t,isSelected:o,attributes:a}=e,{cover:n}=a,[l,s]=(0,_.useState)(!1);(0,_.useEffect)((()=>{1===t?.length&&"api-error"===t[0]&&s(!0)}),[t]);const r={...e,style:(0,p.createElement)(Rt,{...e})};return n.length?(0,p.createElement)("div",null,(0,p.createElement)("img",{src:n,alt:"post grid",style:{maxWidth:"100%"}})):(0,p.createElement)(p.Fragment,null,o&&!l&&(0,p.createElement)(Gt,{...e}),(0,p.createElement)(Zt.Edit,{...r},(0,p.createElement)(p.Fragment,null,l?(0,p.createElement)("p",{className:"elej-error-msg-editor"},"Please Connect your EasyJobs Account"):t?(0,p.createElement)(p.Fragment,null,"default"===t.selected_template&&(0,p.createElement)(It,{props:e}),"classic"===t.selected_template&&(0,p.createElement)(Mt,{props:e}),"elegant"===t.selected_template&&(0,p.createElement)(At,{props:e})):(0,p.createElement)("p",null,"Loading footer block..."))))})),Qt=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"easyjobs/job-footer","version":"0.1.0","title":"Company Gallery","category":"easyjobs","description":"Showcase work culture with team photos and office environment.","editorStyle":"ej-editor-style","supports":{"anchor":true,"align":["wide","full"]},"textdomain":"easyjobs"}');var Kt,Xt,eo;function to(){return to=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var a in o)({}).hasOwnProperty.call(o,a)&&(e[a]=o[a])}return e},to.apply(null,arguments)}(0,b.registerBlockType)(Qt,{icon:function(e){return p.createElement("svg",to({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,fill:"none"},e),Kt||(Kt=p.createElement("path",{fill:"#fff",d:"M0 0h32v32H0z"})),Xt||(Xt=p.createElement("path",{fill:"#597DFC",d:"M24.828 24.428c.568 0 1.023-.455 1.023-1.023 0-.569-.455-1.023-1.023-1.023-.569 0-1.023.454-1.023 1.023 0 .568.454 1.023 1.023 1.023M21.589 22.438a.874.874 0 0 0-.853.853c0 .454.398.852.853.852a.874.874 0 0 0 .853-.852c-.057-.455-.398-.853-.853-.853M27.044 24.769a.39.39 0 0 0-.398.398c0 .227.171.398.398.398.228 0 .398-.17.398-.398a.39.39 0 0 0-.398-.398M23.236 26.02a.57.57 0 0 0 0-*********** 0 0 0-.568.569c.057.34.284.568.568.568M13.515 11.014c.113 0 .227.056.284.056a1.08 1.08 0 1 0 0-2.16 1.08 1.08 0 0 0-1.08 1.08c0 .398.227.796.625.967.057 0 .114.057.17.057"})),eo||(eo=p.createElement("path",{fill:"#597DFC",d:"M20.962 18.233c.796-1.25 1.25-2.786 1.25-4.377 0-3.979-2.842-7.332-6.65-8.128a9 9 0 0 0-1.762-.17c-4.547 0-8.241 3.694-8.241 8.298 0 3.865 2.614 7.162 6.195 8.071-.568-.284-4.547-3.012-4.49-8.298 0-3.41 2.898-6.196 6.309-6.31 3.694-.113 6.764 2.843 6.764 6.537a6.47 6.47 0 0 1-4.036 6.025c-.511.17-2.217.568-3.694-1.421-.967-1.421-.853-2.956-.796-3.41.057-.853.739-1.478 1.534-1.478h.17c.967.057 1.592.739 1.536 1.648-.057.739-.228 1.535-.569 2.217l-.057.057-.17.34c-.057.114-.057.228-.057.342a.524.524 0 0 0 .625.398c.17 0 .284-.114.341-.228.74-1.08 1.25-2.16 1.25-3.467 0-.796-.284-1.535-.795-2.046-.568-.569-1.307-.853-2.16-.853h-.17c-.455 0-.853.114-1.194.284 0 0-1.819.569-1.99 2.672-.17 1.194-.056 3.126 1.706 5.058.227.228.398.455.682.683.284.227.568.397.91.625.17.114.34.17.454.227.284.114.625.227.966.284.74.114 1.478 0 2.16-.284a6.7 6.7 0 0 0 1.535-.852l1.364 1.25c.34-.341.852-.511 1.364-.511.796 0 1.535.511 1.819 1.193.17-.455.568-.796 1.023-.966z"})))},attributes:Ut,edit:Yt,save:()=>null,example:{attributes:{cover:`${EasyJobsLocalize?.image_url}/block-preview/footer.png`}}})})();