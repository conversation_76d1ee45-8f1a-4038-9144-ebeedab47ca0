!function(r){"use strict";r(".ej-social-button").on("click",function(){return window.open(this.href,"","left=20,top=20,width=600,height=300,toolbar=0,resizable=1"),!1}),r(".office__gallery__slider").owlCarousel({center:!0,loop:!0,margin:30,nav:!1,dots:!1,responsive:{0:{items:1},575:{items:3},992:{items:4}}}),r(document).on("submit","#ejJobFilterForm",function(e){var o,t,n=r("#job_title").val(),i=r("#job_category").val(),a=r("#job_location").val(),l=r("#page_id").val();o=a,t=i,n||o||t?(e.preventDefault(),o=window.location.href.split("?")[0],t=[],l&&t.push("page_id="+encodeURIComponent(l)),n&&t.push("job_title="+encodeURIComponent(n)),i&&t.push("job_category="+encodeURIComponent(i)),a&&t.push("job_location="+encodeURIComponent(a)),l=o+"?"+t.join("&"),window.location.href=l):(e.preventDefault(),r("#ejErrorMessage").show())}),r(document).ready(function(){var e=window.location.search,e=new URLSearchParams(e);(e.has("job_page")||e.has("job_location")||e.has("job_category")||e.has("job_title"))&&r("#easyjobs-list")[0].scrollIntoView({behavior:"instant"})}),r(document).on("click","#ej-reset-form",function(e){var o,t=window.location.href.split("?");let n=t[0];1<=t?.length&&t[1]?.includes("page_id")&&(o=t[1]?.split("&"),n+="?"+o[0]),t?.length<=1?e.preventDefault():window.location.href=n}),r(".ej-modal-trigger").on("click",function(e){e.preventDefault(),r(".ej-modal").fadeIn(300)}),r(".ej-modal-close").on("click",function(e){e.preventDefault(),r(".ej-modal").fadeOut(200)})}(jQuery);