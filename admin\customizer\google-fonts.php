<?php

return array(

  'abeezee' => array(
       'id'       => 'abeezee',
       'name'     => "ABeeZee",
       'label'    => "ABeeZee, sans-serif",
       'family'   => "ABeeZee, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       	),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       	),
  ),

  'abel' => array(
       'id'       => 'abel',
       'name'     => "<PERSON>",
       'label'    => "Abel, sans-serif",
       'family'   => "Abel, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'abhaya-libre' => array(
       'id'       => 'abhaya-libre',
       'name'     => "Abhaya Libre",
       'label'    => "'Abhaya Libre', serif",
       'family'   => "Abhaya Libre, serif",
       'category' => "serif",
       'subsets'  => array(
           'sinhala'  => 'Sinhala',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'abril-fatface' => array(
       'id'       => 'abril-fatface',
       'name'     => "Abril Fatface",
       'label'    => "'Abril Fatface', display",
       'family'   => "Abril Fatface, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'aclonica' => array(
       'id'       => 'aclonica',
       'name'     => "Aclonica",
       'label'    => "Aclonica, sans-serif",
       'family'   => "Aclonica, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'acme' => array(
       'id'       => 'acme',
       'name'     => "Acme",
       'label'    => "Acme, sans-serif",
       'family'   => "Acme, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'actor' => array(
       'id'       => 'actor',
       'name'     => "Actor",
       'label'    => "Actor, sans-serif",
       'family'   => "Actor, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'adamina' => array(
       'id'       => 'adamina',
       'name'     => "Adamina",
       'label'    => "Adamina, serif",
       'family'   => "Adamina, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'advent-pro' => array(
       'id'       => 'advent-pro',
       'name'     => "Advent Pro",
       'label'    => "'Advent Pro', sans-serif",
       'family'   => "Advent Pro, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'aguafina-script' => array(
       'id'       => 'aguafina-script',
       'name'     => "Aguafina Script",
       'label'    => "'Aguafina Script', handwriting",
       'family'   => "Aguafina Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'akronim' => array(
       'id'       => 'akronim',
       'name'     => "Akronim",
       'label'    => "Akronim, display",
       'family'   => "Akronim, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'aladin' => array(
       'id'       => 'aladin',
       'name'     => "Aladin",
       'label'    => "Aladin, handwriting",
       'family'   => "Aladin, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'aldrich' => array(
       'id'       => 'aldrich',
       'name'     => "Aldrich",
       'label'    => "Aldrich, sans-serif",
       'family'   => "Aldrich, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'alef' => array(
       'id'       => 'alef',
       'name'     => "Alef",
       'label'    => "Alef, sans-serif",
       'family'   => "Alef, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'hebrew'  => 'Hebrew',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'alegreya' => array(
       'id'       => 'alegreya',
       'name'     => "Alegreya",
       'label'    => "Alegreya, serif",
       'family'   => "Alegreya, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'alegreya-sc' => array(
       'id'       => 'alegreya-sc',
       'name'     => "Alegreya SC",
       'label'    => "'Alegreya SC', serif",
       'family'   => "Alegreya SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'alegreya-sans' => array(
       'id'       => 'alegreya-sans',
       'name'     => "Alegreya Sans",
       'label'    => "'Alegreya Sans', sans-serif",
       'family'   => "Alegreya Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'alegreya-sans-sc' => array(
       'id'       => 'alegreya-sans-sc',
       'name'     => "Alegreya Sans SC",
       'label'    => "'Alegreya Sans SC', sans-serif",
       'family'   => "Alegreya Sans SC, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'alex-brush' => array(
       'id'       => 'alex-brush',
       'name'     => "Alex Brush",
       'label'    => "'Alex Brush', handwriting",
       'family'   => "Alex Brush, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'alfa-slab-one' => array(
       'id'       => 'alfa-slab-one',
       'name'     => "Alfa Slab One",
       'label'    => "'Alfa Slab One', display",
       'family'   => "Alfa Slab One, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'alice' => array(
       'id'       => 'alice',
       'name'     => "Alice",
       'label'    => "Alice, serif",
       'family'   => "Alice, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'alike' => array(
       'id'       => 'alike',
       'name'     => "Alike",
       'label'    => "Alike, serif",
       'family'   => "Alike, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'alike-angular' => array(
       'id'       => 'alike-angular',
       'name'     => "Alike Angular",
       'label'    => "'Alike Angular', serif",
       'family'   => "Alike Angular, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'allan' => array(
       'id'       => 'allan',
       'name'     => "Allan",
       'label'    => "Allan, display",
       'family'   => "Allan, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'allerta' => array(
       'id'       => 'allerta',
       'name'     => "Allerta",
       'label'    => "Allerta, sans-serif",
       'family'   => "Allerta, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'allerta-stencil' => array(
       'id'       => 'allerta-stencil',
       'name'     => "Allerta Stencil",
       'label'    => "'Allerta Stencil', sans-serif",
       'family'   => "Allerta Stencil, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'allura' => array(
       'id'       => 'allura',
       'name'     => "Allura",
       'label'    => "Allura, handwriting",
       'family'   => "Allura, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'almendra' => array(
       'id'       => 'almendra',
       'name'     => "Almendra",
       'label'    => "Almendra, serif",
       'family'   => "Almendra, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'almendra-display' => array(
       'id'       => 'almendra-display',
       'name'     => "Almendra Display",
       'label'    => "'Almendra Display', display",
       'family'   => "Almendra Display, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'almendra-sc' => array(
       'id'       => 'almendra-sc',
       'name'     => "Almendra SC",
       'label'    => "'Almendra SC', serif",
       'family'   => "Almendra SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'amarante' => array(
       'id'       => 'amarante',
       'name'     => "Amarante",
       'label'    => "Amarante, display",
       'family'   => "Amarante, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'amaranth' => array(
       'id'       => 'amaranth',
       'name'     => "Amaranth",
       'label'    => "Amaranth, sans-serif",
       'family'   => "Amaranth, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'amatic-sc' => array(
       'id'       => 'amatic-sc',
       'name'     => "Amatic SC",
       'label'    => "'Amatic SC', handwriting",
       'family'   => "Amatic SC, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'amatica-sc' => array(
       'id'       => 'amatica-sc',
       'name'     => "Amatica SC",
       'label'    => "'Amatica SC', display",
       'family'   => "Amatica SC, cursive",
       'category' => "display",
       'subsets'  => array(
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'amethysta' => array(
       'id'       => 'amethysta',
       'name'     => "Amethysta",
       'label'    => "Amethysta, serif",
       'family'   => "Amethysta, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'amiko' => array(
       'id'       => 'amiko',
       'name'     => "Amiko",
       'label'    => "Amiko, sans-serif",
       'family'   => "Amiko, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'amiri' => array(
       'id'       => 'amiri',
       'name'     => "Amiri",
       'label'    => "Amiri, serif",
       'family'   => "Amiri, serif",
       'category' => "serif",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'amita' => array(
       'id'       => 'amita',
       'name'     => "Amita",
       'label'    => "Amita, handwriting",
       'family'   => "Amita, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'anaheim' => array(
       'id'       => 'anaheim',
       'name'     => "Anaheim",
       'label'    => "Anaheim, sans-serif",
       'family'   => "Anaheim, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'andada' => array(
       'id'       => 'andada',
       'name'     => "Andada",
       'label'    => "Andada, serif",
       'family'   => "Andada, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'andika' => array(
       'id'       => 'andika',
       'name'     => "Andika",
       'label'    => "Andika, sans-serif",
       'family'   => "Andika, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'angkor' => array(
       'id'       => 'angkor',
       'name'     => "Angkor",
       'label'    => "Angkor, display",
       'family'   => "Angkor, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'annie-use-your-telescope' => array(
       'id'       => 'annie-use-your-telescope',
       'name'     => "Annie Use Your Telescope",
       'label'    => "'Annie Use Your Telescope', handwriting",
       'family'   => "Annie Use Your Telescope, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'anonymous-pro' => array(
       'id'       => 'anonymous-pro',
       'name'     => "Anonymous Pro",
       'label'    => "'Anonymous Pro', monospace",
       'family'   => "Anonymous Pro, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'antic' => array(
       'id'       => 'antic',
       'name'     => "Antic",
       'label'    => "Antic, sans-serif",
       'family'   => "Antic, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'antic-didone' => array(
       'id'       => 'antic-didone',
       'name'     => "Antic Didone",
       'label'    => "'Antic Didone', serif",
       'family'   => "Antic Didone, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'antic-slab' => array(
       'id'       => 'antic-slab',
       'name'     => "Antic Slab",
       'label'    => "'Antic Slab', serif",
       'family'   => "Antic Slab, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'anton' => array(
       'id'       => 'anton',
       'name'     => "Anton",
       'label'    => "Anton, sans-serif",
       'family'   => "Anton, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'arapey' => array(
       'id'       => 'arapey',
       'name'     => "Arapey",
       'label'    => "Arapey, serif",
       'family'   => "Arapey, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'arbutus' => array(
       'id'       => 'arbutus',
       'name'     => "Arbutus",
       'label'    => "Arbutus, display",
       'family'   => "Arbutus, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'arbutus-slab' => array(
       'id'       => 'arbutus-slab',
       'name'     => "Arbutus Slab",
       'label'    => "'Arbutus Slab', serif",
       'family'   => "Arbutus Slab, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'architects-daughter' => array(
       'id'       => 'architects-daughter',
       'name'     => "Architects Daughter",
       'label'    => "'Architects Daughter', handwriting",
       'family'   => "Architects Daughter, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'archivo-black' => array(
       'id'       => 'archivo-black',
       'name'     => "Archivo Black",
       'label'    => "'Archivo Black', sans-serif",
       'family'   => "Archivo Black, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'archivo-narrow' => array(
       'id'       => 'archivo-narrow',
       'name'     => "Archivo Narrow",
       'label'    => "'Archivo Narrow', sans-serif",
       'family'   => "Archivo Narrow, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'aref-ruqaa' => array(
       'id'       => 'aref-ruqaa',
       'name'     => "Aref Ruqaa",
       'label'    => "'Aref Ruqaa', serif",
       'family'   => "Aref Ruqaa, serif",
       'category' => "serif",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'arima-madurai' => array(
       'id'       => 'arima-madurai',
       'name'     => "Arima Madurai",
       'label'    => "'Arima Madurai', display",
       'family'   => "Arima Madurai, cursive",
       'category' => "display",
       'subsets'  => array(
           'tamil'  => 'Tamil',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'arimo' => array(
       'id'       => 'arimo',
       'name'     => "Arimo",
       'label'    => "Arimo, sans-serif",
       'family'   => "Arimo, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'arizonia' => array(
       'id'       => 'arizonia',
       'name'     => "Arizonia",
       'label'    => "Arizonia, handwriting",
       'family'   => "Arizonia, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'armata' => array(
       'id'       => 'armata',
       'name'     => "Armata",
       'label'    => "Armata, sans-serif",
       'family'   => "Armata, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'arsenal' => array(
       'id'       => 'arsenal',
       'name'     => "Arsenal",
       'label'    => "Arsenal, sans-serif",
       'family'   => "Arsenal, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'artifika' => array(
       'id'       => 'artifika',
       'name'     => "Artifika",
       'label'    => "Artifika, serif",
       'family'   => "Artifika, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'arvo' => array(
       'id'       => 'arvo',
       'name'     => "Arvo",
       'label'    => "Arvo, serif",
       'family'   => "Arvo, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'arya' => array(
       'id'       => 'arya',
       'name'     => "Arya",
       'label'    => "Arya, sans-serif",
       'family'   => "Arya, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'asap' => array(
       'id'       => 'asap',
       'name'     => "Asap",
       'label'    => "Asap, sans-serif",
       'family'   => "Asap, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'asar' => array(
       'id'       => 'asar',
       'name'     => "Asar",
       'label'    => "Asar, serif",
       'family'   => "Asar, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'asset' => array(
       'id'       => 'asset',
       'name'     => "Asset",
       'label'    => "Asset, display",
       'family'   => "Asset, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'assistant' => array(
       'id'       => 'assistant',
       'name'     => "Assistant",
       'label'    => "Assistant, sans-serif",
       'family'   => "Assistant, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'hebrew'  => 'Hebrew',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'astloch' => array(
       'id'       => 'astloch',
       'name'     => "Astloch",
       'label'    => "Astloch, display",
       'family'   => "Astloch, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'asul' => array(
       'id'       => 'asul',
       'name'     => "Asul",
       'label'    => "Asul, sans-serif",
       'family'   => "Asul, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'athiti' => array(
       'id'       => 'athiti',
       'name'     => "Athiti",
       'label'    => "Athiti, sans-serif",
       'family'   => "Athiti, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'atma' => array(
       'id'       => 'atma',
       'name'     => "Atma",
       'label'    => "Atma, display",
       'family'   => "Atma, cursive",
       'category' => "display",
       'subsets'  => array(
           'bengali'  => 'Bengali',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'atomic-age' => array(
       'id'       => 'atomic-age',
       'name'     => "Atomic Age",
       'label'    => "'Atomic Age', display",
       'family'   => "Atomic Age, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'aubrey' => array(
       'id'       => 'aubrey',
       'name'     => "Aubrey",
       'label'    => "Aubrey, display",
       'family'   => "Aubrey, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'audiowide' => array(
       'id'       => 'audiowide',
       'name'     => "Audiowide",
       'label'    => "Audiowide, display",
       'family'   => "Audiowide, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'autour-one' => array(
       'id'       => 'autour-one',
       'name'     => "Autour One",
       'label'    => "'Autour One', display",
       'family'   => "Autour One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'average' => array(
       'id'       => 'average',
       'name'     => "Average",
       'label'    => "Average, serif",
       'family'   => "Average, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'average-sans' => array(
       'id'       => 'average-sans',
       'name'     => "Average Sans",
       'label'    => "'Average Sans', sans-serif",
       'family'   => "Average Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'averia-gruesa-libre' => array(
       'id'       => 'averia-gruesa-libre',
       'name'     => "Averia Gruesa Libre",
       'label'    => "'Averia Gruesa Libre', display",
       'family'   => "Averia Gruesa Libre, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'averia-libre' => array(
       'id'       => 'averia-libre',
       'name'     => "Averia Libre",
       'label'    => "'Averia Libre', display",
       'family'   => "Averia Libre, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'averia-sans-libre' => array(
       'id'       => 'averia-sans-libre',
       'name'     => "Averia Sans Libre",
       'label'    => "'Averia Sans Libre', display",
       'family'   => "Averia Sans Libre, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'averia-serif-libre' => array(
       'id'       => 'averia-serif-libre',
       'name'     => "Averia Serif Libre",
       'label'    => "'Averia Serif Libre', display",
       'family'   => "Averia Serif Libre, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'bad-script' => array(
       'id'       => 'bad-script',
       'name'     => "Bad Script",
       'label'    => "'Bad Script', handwriting",
       'family'   => "Bad Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bahiana' => array(
       'id'       => 'bahiana',
       'name'     => "Bahiana",
       'label'    => "Bahiana, display",
       'family'   => "Bahiana, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'baloo' => array(
       'id'       => 'baloo',
       'name'     => "Baloo",
       'label'    => "Baloo, display",
       'family'   => "Baloo, cursive",
       'category' => "display",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'baloo-bhai' => array(
       'id'       => 'baloo-bhai',
       'name'     => "Baloo Bhai",
       'label'    => "'Baloo Bhai', display",
       'family'   => "Baloo Bhai, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'baloo-bhaina' => array(
       'id'       => 'baloo-bhaina',
       'name'     => "Baloo Bhaina",
       'label'    => "'Baloo Bhaina', display",
       'family'   => "Baloo Bhaina, cursive",
       'category' => "display",
       'subsets'  => array(
           'oriya'  => 'undefined',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'baloo-chettan' => array(
       'id'       => 'baloo-chettan',
       'name'     => "Baloo Chettan",
       'label'    => "'Baloo Chettan', display",
       'family'   => "Baloo Chettan, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'malayalam'  => 'undefined',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'baloo-da' => array(
       'id'       => 'baloo-da',
       'name'     => "Baloo Da",
       'label'    => "'Baloo Da', display",
       'family'   => "Baloo Da, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'bengali'  => 'Bengali',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'baloo-paaji' => array(
       'id'       => 'baloo-paaji',
       'name'     => "Baloo Paaji",
       'label'    => "'Baloo Paaji', display",
       'family'   => "Baloo Paaji, cursive",
       'category' => "display",
       'subsets'  => array(
           'gurmukhi'  => 'undefined',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'baloo-tamma' => array(
       'id'       => 'baloo-tamma',
       'name'     => "Baloo Tamma",
       'label'    => "'Baloo Tamma', display",
       'family'   => "Baloo Tamma, cursive",
       'category' => "display",
       'subsets'  => array(
           'kannada'  => 'undefined',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'baloo-thambi' => array(
       'id'       => 'baloo-thambi',
       'name'     => "Baloo Thambi",
       'label'    => "'Baloo Thambi', display",
       'family'   => "Baloo Thambi, cursive",
       'category' => "display",
       'subsets'  => array(
           'tamil'  => 'Tamil',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'balthazar' => array(
       'id'       => 'balthazar',
       'name'     => "Balthazar",
       'label'    => "Balthazar, serif",
       'family'   => "Balthazar, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bangers' => array(
       'id'       => 'bangers',
       'name'     => "Bangers",
       'label'    => "Bangers, display",
       'family'   => "Bangers, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'barrio' => array(
       'id'       => 'barrio',
       'name'     => "Barrio",
       'label'    => "Barrio, display",
       'family'   => "Barrio, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'basic' => array(
       'id'       => 'basic',
       'name'     => "Basic",
       'label'    => "Basic, sans-serif",
       'family'   => "Basic, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'battambang' => array(
       'id'       => 'battambang',
       'name'     => "Battambang",
       'label'    => "Battambang, display",
       'family'   => "Battambang, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'baumans' => array(
       'id'       => 'baumans',
       'name'     => "Baumans",
       'label'    => "Baumans, display",
       'family'   => "Baumans, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bayon' => array(
       'id'       => 'bayon',
       'name'     => "Bayon",
       'label'    => "Bayon, display",
       'family'   => "Bayon, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'belgrano' => array(
       'id'       => 'belgrano',
       'name'     => "Belgrano",
       'label'    => "Belgrano, serif",
       'family'   => "Belgrano, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'belleza' => array(
       'id'       => 'belleza',
       'name'     => "Belleza",
       'label'    => "Belleza, sans-serif",
       'family'   => "Belleza, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'benchnine' => array(
       'id'       => 'benchnine',
       'name'     => "BenchNine",
       'label'    => "BenchNine, sans-serif",
       'family'   => "BenchNine, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'bentham' => array(
       'id'       => 'bentham',
       'name'     => "Bentham",
       'label'    => "Bentham, serif",
       'family'   => "Bentham, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'berkshire-swash' => array(
       'id'       => 'berkshire-swash',
       'name'     => "Berkshire Swash",
       'label'    => "'Berkshire Swash', handwriting",
       'family'   => "Berkshire Swash, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bevan' => array(
       'id'       => 'bevan',
       'name'     => "Bevan",
       'label'    => "Bevan, display",
       'family'   => "Bevan, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bigelow-rules' => array(
       'id'       => 'bigelow-rules',
       'name'     => "Bigelow Rules",
       'label'    => "'Bigelow Rules', display",
       'family'   => "Bigelow Rules, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bigshot-one' => array(
       'id'       => 'bigshot-one',
       'name'     => "Bigshot One",
       'label'    => "'Bigshot One', display",
       'family'   => "Bigshot One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bilbo' => array(
       'id'       => 'bilbo',
       'name'     => "Bilbo",
       'label'    => "Bilbo, handwriting",
       'family'   => "Bilbo, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bilbo-swash-caps' => array(
       'id'       => 'bilbo-swash-caps',
       'name'     => "Bilbo Swash Caps",
       'label'    => "'Bilbo Swash Caps', handwriting",
       'family'   => "Bilbo Swash Caps, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'biorhyme' => array(
       'id'       => 'biorhyme',
       'name'     => "BioRhyme",
       'label'    => "BioRhyme, serif",
       'family'   => "BioRhyme, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'biorhyme-expanded' => array(
       'id'       => 'biorhyme-expanded',
       'name'     => "BioRhyme Expanded",
       'label'    => "'BioRhyme Expanded', serif",
       'family'   => "BioRhyme Expanded, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'biryani' => array(
       'id'       => 'biryani',
       'name'     => "Biryani",
       'label'    => "Biryani, sans-serif",
       'family'   => "Biryani, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'bitter' => array(
       'id'       => 'bitter',
       'name'     => "Bitter",
       'label'    => "Bitter, serif",
       'family'   => "Bitter, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'black-ops-one' => array(
       'id'       => 'black-ops-one',
       'name'     => "Black Ops One",
       'label'    => "'Black Ops One', display",
       'family'   => "Black Ops One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bokor' => array(
       'id'       => 'bokor',
       'name'     => "Bokor",
       'label'    => "Bokor, display",
       'family'   => "Bokor, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bonbon' => array(
       'id'       => 'bonbon',
       'name'     => "Bonbon",
       'label'    => "Bonbon, handwriting",
       'family'   => "Bonbon, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'boogaloo' => array(
       'id'       => 'boogaloo',
       'name'     => "Boogaloo",
       'label'    => "Boogaloo, display",
       'family'   => "Boogaloo, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bowlby-one' => array(
       'id'       => 'bowlby-one',
       'name'     => "Bowlby One",
       'label'    => "'Bowlby One', display",
       'family'   => "Bowlby One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bowlby-one-sc' => array(
       'id'       => 'bowlby-one-sc',
       'name'     => "Bowlby One SC",
       'label'    => "'Bowlby One SC', display",
       'family'   => "Bowlby One SC, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'brawler' => array(
       'id'       => 'brawler',
       'name'     => "Brawler",
       'label'    => "Brawler, serif",
       'family'   => "Brawler, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bree-serif' => array(
       'id'       => 'bree-serif',
       'name'     => "Bree Serif",
       'label'    => "'Bree Serif', serif",
       'family'   => "Bree Serif, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bubblegum-sans' => array(
       'id'       => 'bubblegum-sans',
       'name'     => "Bubblegum Sans",
       'label'    => "'Bubblegum Sans', display",
       'family'   => "Bubblegum Sans, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bubbler-one' => array(
       'id'       => 'bubbler-one',
       'name'     => "Bubbler One",
       'label'    => "'Bubbler One', sans-serif",
       'family'   => "Bubbler One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'buda' => array(
       'id'       => 'buda',
       'name'     => "Buda",
       'label'    => "Buda, display",
       'family'   => "Buda, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
       ),
  ),

  'buenard' => array(
       'id'       => 'buenard',
       'name'     => "Buenard",
       'label'    => "Buenard, serif",
       'family'   => "Buenard, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'bungee' => array(
       'id'       => 'bungee',
       'name'     => "Bungee",
       'label'    => "Bungee, display",
       'family'   => "Bungee, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bungee-hairline' => array(
       'id'       => 'bungee-hairline',
       'name'     => "Bungee Hairline",
       'label'    => "'Bungee Hairline', display",
       'family'   => "Bungee Hairline, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bungee-inline' => array(
       'id'       => 'bungee-inline',
       'name'     => "Bungee Inline",
       'label'    => "'Bungee Inline', display",
       'family'   => "Bungee Inline, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bungee-outline' => array(
       'id'       => 'bungee-outline',
       'name'     => "Bungee Outline",
       'label'    => "'Bungee Outline', display",
       'family'   => "Bungee Outline, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'bungee-shade' => array(
       'id'       => 'bungee-shade',
       'name'     => "Bungee Shade",
       'label'    => "'Bungee Shade', display",
       'family'   => "Bungee Shade, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'butcherman' => array(
       'id'       => 'butcherman',
       'name'     => "Butcherman",
       'label'    => "Butcherman, display",
       'family'   => "Butcherman, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'butterfly-kids' => array(
       'id'       => 'butterfly-kids',
       'name'     => "Butterfly Kids",
       'label'    => "'Butterfly Kids', handwriting",
       'family'   => "Butterfly Kids, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cabin' => array(
       'id'       => 'cabin',
       'name'     => "Cabin",
       'label'    => "Cabin, sans-serif",
       'family'   => "Cabin, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'cabin-condensed' => array(
       'id'       => 'cabin-condensed',
       'name'     => "Cabin Condensed",
       'label'    => "'Cabin Condensed', sans-serif",
       'family'   => "Cabin Condensed, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'cabin-sketch' => array(
       'id'       => 'cabin-sketch',
       'name'     => "Cabin Sketch",
       'label'    => "'Cabin Sketch', display",
       'family'   => "Cabin Sketch, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'caesar-dressing' => array(
       'id'       => 'caesar-dressing',
       'name'     => "Caesar Dressing",
       'label'    => "'Caesar Dressing', display",
       'family'   => "Caesar Dressing, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cagliostro' => array(
       'id'       => 'cagliostro',
       'name'     => "Cagliostro",
       'label'    => "Cagliostro, sans-serif",
       'family'   => "Cagliostro, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cairo' => array(
       'id'       => 'cairo',
       'name'     => "Cairo",
       'label'    => "Cairo, sans-serif",
       'family'   => "Cairo, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'calligraffitti' => array(
       'id'       => 'calligraffitti',
       'name'     => "Calligraffitti",
       'label'    => "Calligraffitti, handwriting",
       'family'   => "Calligraffitti, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cambay' => array(
       'id'       => 'cambay',
       'name'     => "Cambay",
       'label'    => "Cambay, sans-serif",
       'family'   => "Cambay, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'cambo' => array(
       'id'       => 'cambo',
       'name'     => "Cambo",
       'label'    => "Cambo, serif",
       'family'   => "Cambo, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'candal' => array(
       'id'       => 'candal',
       'name'     => "Candal",
       'label'    => "Candal, sans-serif",
       'family'   => "Candal, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cantarell' => array(
       'id'       => 'cantarell',
       'name'     => "Cantarell",
       'label'    => "Cantarell, sans-serif",
       'family'   => "Cantarell, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'cantata-one' => array(
       'id'       => 'cantata-one',
       'name'     => "Cantata One",
       'label'    => "'Cantata One', serif",
       'family'   => "Cantata One, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cantora-one' => array(
       'id'       => 'cantora-one',
       'name'     => "Cantora One",
       'label'    => "'Cantora One', sans-serif",
       'family'   => "Cantora One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'capriola' => array(
       'id'       => 'capriola',
       'name'     => "Capriola",
       'label'    => "Capriola, sans-serif",
       'family'   => "Capriola, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cardo' => array(
       'id'       => 'cardo',
       'name'     => "Cardo",
       'label'    => "Cardo, serif",
       'family'   => "Cardo, serif",
       'category' => "serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'latin-ext'  => 'Latin Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'carme' => array(
       'id'       => 'carme',
       'name'     => "Carme",
       'label'    => "Carme, sans-serif",
       'family'   => "Carme, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'carrois-gothic' => array(
       'id'       => 'carrois-gothic',
       'name'     => "Carrois Gothic",
       'label'    => "'Carrois Gothic', sans-serif",
       'family'   => "Carrois Gothic, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'carrois-gothic-sc' => array(
       'id'       => 'carrois-gothic-sc',
       'name'     => "Carrois Gothic SC",
       'label'    => "'Carrois Gothic SC', sans-serif",
       'family'   => "Carrois Gothic SC, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'carter-one' => array(
       'id'       => 'carter-one',
       'name'     => "Carter One",
       'label'    => "'Carter One', display",
       'family'   => "Carter One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'catamaran' => array(
       'id'       => 'catamaran',
       'name'     => "Catamaran",
       'label'    => "Catamaran, sans-serif",
       'family'   => "Catamaran, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'tamil'  => 'Tamil',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'caudex' => array(
       'id'       => 'caudex',
       'name'     => "Caudex",
       'label'    => "Caudex, serif",
       'family'   => "Caudex, serif",
       'category' => "serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'latin-ext'  => 'Latin Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'caveat' => array(
       'id'       => 'caveat',
       'name'     => "Caveat",
       'label'    => "Caveat, handwriting",
       'family'   => "Caveat, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'caveat-brush' => array(
       'id'       => 'caveat-brush',
       'name'     => "Caveat Brush",
       'label'    => "'Caveat Brush', handwriting",
       'family'   => "Caveat Brush, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cedarville-cursive' => array(
       'id'       => 'cedarville-cursive',
       'name'     => "Cedarville Cursive",
       'label'    => "'Cedarville Cursive', handwriting",
       'family'   => "Cedarville Cursive, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ceviche-one' => array(
       'id'       => 'ceviche-one',
       'name'     => "Ceviche One",
       'label'    => "'Ceviche One', display",
       'family'   => "Ceviche One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'changa' => array(
       'id'       => 'changa',
       'name'     => "Changa",
       'label'    => "Changa, sans-serif",
       'family'   => "Changa, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'changa-one' => array(
       'id'       => 'changa-one',
       'name'     => "Changa One",
       'label'    => "'Changa One', display",
       'family'   => "Changa One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'chango' => array(
       'id'       => 'chango',
       'name'     => "Chango",
       'label'    => "Chango, display",
       'family'   => "Chango, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'chathura' => array(
       'id'       => 'chathura',
       'name'     => "Chathura",
       'label'    => "Chathura, sans-serif",
       'family'   => "Chathura, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'chau-philomene-one' => array(
       'id'       => 'chau-philomene-one',
       'name'     => "Chau Philomene One",
       'label'    => "'Chau Philomene One', sans-serif",
       'family'   => "Chau Philomene One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'chela-one' => array(
       'id'       => 'chela-one',
       'name'     => "Chela One",
       'label'    => "'Chela One', display",
       'family'   => "Chela One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'chelsea-market' => array(
       'id'       => 'chelsea-market',
       'name'     => "Chelsea Market",
       'label'    => "'Chelsea Market', display",
       'family'   => "Chelsea Market, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'chenla' => array(
       'id'       => 'chenla',
       'name'     => "Chenla",
       'label'    => "Chenla, display",
       'family'   => "Chenla, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cherry-cream-soda' => array(
       'id'       => 'cherry-cream-soda',
       'name'     => "Cherry Cream Soda",
       'label'    => "'Cherry Cream Soda', display",
       'family'   => "Cherry Cream Soda, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cherry-swash' => array(
       'id'       => 'cherry-swash',
       'name'     => "Cherry Swash",
       'label'    => "'Cherry Swash', display",
       'family'   => "Cherry Swash, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'chewy' => array(
       'id'       => 'chewy',
       'name'     => "Chewy",
       'label'    => "Chewy, display",
       'family'   => "Chewy, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'chicle' => array(
       'id'       => 'chicle',
       'name'     => "Chicle",
       'label'    => "Chicle, display",
       'family'   => "Chicle, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'chivo' => array(
       'id'       => 'chivo',
       'name'     => "Chivo",
       'label'    => "Chivo, sans-serif",
       'family'   => "Chivo, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'chonburi' => array(
       'id'       => 'chonburi',
       'name'     => "Chonburi",
       'label'    => "Chonburi, display",
       'family'   => "Chonburi, cursive",
       'category' => "display",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cinzel' => array(
       'id'       => 'cinzel',
       'name'     => "Cinzel",
       'label'    => "Cinzel, serif",
       'family'   => "Cinzel, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'cinzel-decorative' => array(
       'id'       => 'cinzel-decorative',
       'name'     => "Cinzel Decorative",
       'label'    => "'Cinzel Decorative', display",
       'family'   => "Cinzel Decorative, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'clicker-script' => array(
       'id'       => 'clicker-script',
       'name'     => "Clicker Script",
       'label'    => "'Clicker Script', handwriting",
       'family'   => "Clicker Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'coda' => array(
       'id'       => 'coda',
       'name'     => "Coda",
       'label'    => "Coda, display",
       'family'   => "Coda, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'coda-caption' => array(
       'id'       => 'coda-caption',
       'name'     => "Coda Caption",
       'label'    => "'Coda Caption', sans-serif",
       'family'   => "Coda Caption, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '800'  =>  'Extra bold 800',
       ),
  ),

  'codystar' => array(
       'id'       => 'codystar',
       'name'     => "Codystar",
       'label'    => "Codystar, display",
       'family'   => "Codystar, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
       ),
  ),

  'coiny' => array(
       'id'       => 'coiny',
       'name'     => "Coiny",
       'label'    => "Coiny, display",
       'family'   => "Coiny, cursive",
       'category' => "display",
       'subsets'  => array(
           'tamil'  => 'Tamil',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'combo' => array(
       'id'       => 'combo',
       'name'     => "Combo",
       'label'    => "Combo, display",
       'family'   => "Combo, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'comfortaa' => array(
       'id'       => 'comfortaa',
       'name'     => "Comfortaa",
       'label'    => "Comfortaa, display",
       'family'   => "Comfortaa, cursive",
       'category' => "display",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'coming-soon' => array(
       'id'       => 'coming-soon',
       'name'     => "Coming Soon",
       'label'    => "'Coming Soon', handwriting",
       'family'   => "Coming Soon, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'concert-one' => array(
       'id'       => 'concert-one',
       'name'     => "Concert One",
       'label'    => "'Concert One', display",
       'family'   => "Concert One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'condiment' => array(
       'id'       => 'condiment',
       'name'     => "Condiment",
       'label'    => "Condiment, handwriting",
       'family'   => "Condiment, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'content' => array(
       'id'       => 'content',
       'name'     => "Content",
       'label'    => "Content, display",
       'family'   => "Content, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'contrail-one' => array(
       'id'       => 'contrail-one',
       'name'     => "Contrail One",
       'label'    => "'Contrail One', display",
       'family'   => "Contrail One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'convergence' => array(
       'id'       => 'convergence',
       'name'     => "Convergence",
       'label'    => "Convergence, sans-serif",
       'family'   => "Convergence, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cookie' => array(
       'id'       => 'cookie',
       'name'     => "Cookie",
       'label'    => "Cookie, handwriting",
       'family'   => "Cookie, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'copse' => array(
       'id'       => 'copse',
       'name'     => "Copse",
       'label'    => "Copse, serif",
       'family'   => "Copse, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'corben' => array(
       'id'       => 'corben',
       'name'     => "Corben",
       'label'    => "Corben, display",
       'family'   => "Corben, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'cormorant' => array(
       'id'       => 'cormorant',
       'name'     => "Cormorant",
       'label'    => "Cormorant, serif",
       'family'   => "Cormorant, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'cormorant-garamond' => array(
       'id'       => 'cormorant-garamond',
       'name'     => "Cormorant Garamond",
       'label'    => "'Cormorant Garamond', serif",
       'family'   => "Cormorant Garamond, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'cormorant-infant' => array(
       'id'       => 'cormorant-infant',
       'name'     => "Cormorant Infant",
       'label'    => "'Cormorant Infant', serif",
       'family'   => "Cormorant Infant, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'cormorant-sc' => array(
       'id'       => 'cormorant-sc',
       'name'     => "Cormorant SC",
       'label'    => "'Cormorant SC', serif",
       'family'   => "Cormorant SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'cormorant-unicase' => array(
       'id'       => 'cormorant-unicase',
       'name'     => "Cormorant Unicase",
       'label'    => "'Cormorant Unicase', serif",
       'family'   => "Cormorant Unicase, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'cormorant-upright' => array(
       'id'       => 'cormorant-upright',
       'name'     => "Cormorant Upright",
       'label'    => "'Cormorant Upright', serif",
       'family'   => "Cormorant Upright, serif",
       'category' => "serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'courgette' => array(
       'id'       => 'courgette',
       'name'     => "Courgette",
       'label'    => "Courgette, handwriting",
       'family'   => "Courgette, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cousine' => array(
       'id'       => 'cousine',
       'name'     => "Cousine",
       'label'    => "Cousine, monospace",
       'family'   => "Cousine, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'coustard' => array(
       'id'       => 'coustard',
       'name'     => "Coustard",
       'label'    => "Coustard, serif",
       'family'   => "Coustard, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '900'  =>  'Black 900',
       ),
  ),

  'covered-by-your-grace' => array(
       'id'       => 'covered-by-your-grace',
       'name'     => "Covered By Your Grace",
       'label'    => "'Covered By Your Grace', handwriting",
       'family'   => "Covered By Your Grace, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'crafty-girls' => array(
       'id'       => 'crafty-girls',
       'name'     => "Crafty Girls",
       'label'    => "'Crafty Girls', handwriting",
       'family'   => "Crafty Girls, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'creepster' => array(
       'id'       => 'creepster',
       'name'     => "Creepster",
       'label'    => "Creepster, display",
       'family'   => "Creepster, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'crete-round' => array(
       'id'       => 'crete-round',
       'name'     => "Crete Round",
       'label'    => "'Crete Round', serif",
       'family'   => "Crete Round, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'crimson-text' => array(
       'id'       => 'crimson-text',
       'name'     => "Crimson Text",
       'label'    => "'Crimson Text', serif",
       'family'   => "Crimson Text, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'croissant-one' => array(
       'id'       => 'croissant-one',
       'name'     => "Croissant One",
       'label'    => "'Croissant One', display",
       'family'   => "Croissant One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'crushed' => array(
       'id'       => 'crushed',
       'name'     => "Crushed",
       'label'    => "Crushed, display",
       'family'   => "Crushed, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cuprum' => array(
       'id'       => 'cuprum',
       'name'     => "Cuprum",
       'label'    => "Cuprum, sans-serif",
       'family'   => "Cuprum, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'cutive' => array(
       'id'       => 'cutive',
       'name'     => "Cutive",
       'label'    => "Cutive, serif",
       'family'   => "Cutive, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'cutive-mono' => array(
       'id'       => 'cutive-mono',
       'name'     => "Cutive Mono",
       'label'    => "'Cutive Mono', monospace",
       'family'   => "Cutive Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'damion' => array(
       'id'       => 'damion',
       'name'     => "Damion",
       'label'    => "Damion, handwriting",
       'family'   => "Damion, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'dancing-script' => array(
       'id'       => 'dancing-script',
       'name'     => "Dancing Script",
       'label'    => "'Dancing Script', handwriting",
       'family'   => "Dancing Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'dangrek' => array(
       'id'       => 'dangrek',
       'name'     => "Dangrek",
       'label'    => "Dangrek, display",
       'family'   => "Dangrek, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'david-libre' => array(
       'id'       => 'david-libre',
       'name'     => "David Libre",
       'label'    => "'David Libre', serif",
       'family'   => "David Libre, serif",
       'category' => "serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
       ),
  ),

  'dawning-of-a-new-day' => array(
       'id'       => 'dawning-of-a-new-day',
       'name'     => "Dawning of a New Day",
       'label'    => "'Dawning of a New Day', handwriting",
       'family'   => "Dawning of a New Day, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'days-one' => array(
       'id'       => 'days-one',
       'name'     => "Days One",
       'label'    => "'Days One', sans-serif",
       'family'   => "Days One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'dekko' => array(
       'id'       => 'dekko',
       'name'     => "Dekko",
       'label'    => "Dekko, handwriting",
       'family'   => "Dekko, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'delius' => array(
       'id'       => 'delius',
       'name'     => "Delius",
       'label'    => "Delius, handwriting",
       'family'   => "Delius, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'delius-swash-caps' => array(
       'id'       => 'delius-swash-caps',
       'name'     => "Delius Swash Caps",
       'label'    => "'Delius Swash Caps', handwriting",
       'family'   => "Delius Swash Caps, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'delius-unicase' => array(
       'id'       => 'delius-unicase',
       'name'     => "Delius Unicase",
       'label'    => "'Delius Unicase', handwriting",
       'family'   => "Delius Unicase, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'della-respira' => array(
       'id'       => 'della-respira',
       'name'     => "Della Respira",
       'label'    => "'Della Respira', serif",
       'family'   => "Della Respira, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'denk-one' => array(
       'id'       => 'denk-one',
       'name'     => "Denk One",
       'label'    => "'Denk One', sans-serif",
       'family'   => "Denk One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'devonshire' => array(
       'id'       => 'devonshire',
       'name'     => "Devonshire",
       'label'    => "Devonshire, handwriting",
       'family'   => "Devonshire, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'dhurjati' => array(
       'id'       => 'dhurjati',
       'name'     => "Dhurjati",
       'label'    => "Dhurjati, sans-serif",
       'family'   => "Dhurjati, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'didact-gothic' => array(
       'id'       => 'didact-gothic',
       'name'     => "Didact Gothic",
       'label'    => "'Didact Gothic', sans-serif",
       'family'   => "Didact Gothic, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'diplomata' => array(
       'id'       => 'diplomata',
       'name'     => "Diplomata",
       'label'    => "Diplomata, display",
       'family'   => "Diplomata, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'diplomata-sc' => array(
       'id'       => 'diplomata-sc',
       'name'     => "Diplomata SC",
       'label'    => "'Diplomata SC', display",
       'family'   => "Diplomata SC, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'domine' => array(
       'id'       => 'domine',
       'name'     => "Domine",
       'label'    => "Domine, serif",
       'family'   => "Domine, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'donegal-one' => array(
       'id'       => 'donegal-one',
       'name'     => "Donegal One",
       'label'    => "'Donegal One', serif",
       'family'   => "Donegal One, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'doppio-one' => array(
       'id'       => 'doppio-one',
       'name'     => "Doppio One",
       'label'    => "'Doppio One', sans-serif",
       'family'   => "Doppio One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'dorsa' => array(
       'id'       => 'dorsa',
       'name'     => "Dorsa",
       'label'    => "Dorsa, sans-serif",
       'family'   => "Dorsa, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'dosis' => array(
       'id'       => 'dosis',
       'name'     => "Dosis",
       'label'    => "Dosis, sans-serif",
       'family'   => "Dosis, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'dr-sugiyama' => array(
       'id'       => 'dr-sugiyama',
       'name'     => "Dr Sugiyama",
       'label'    => "'Dr Sugiyama', handwriting",
       'family'   => "Dr Sugiyama, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'droid-sans' => array(
       'id'       => 'droid-sans',
       'name'     => "Droid Sans",
       'label'    => "'Droid Sans', sans-serif",
       'family'   => "Droid Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'droid-sans-mono' => array(
       'id'       => 'droid-sans-mono',
       'name'     => "Droid Sans Mono",
       'label'    => "'Droid Sans Mono', monospace",
       'family'   => "Droid Sans Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'droid-serif' => array(
       'id'       => 'droid-serif',
       'name'     => "Droid Serif",
       'label'    => "'Droid Serif', serif",
       'family'   => "Droid Serif, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'duru-sans' => array(
       'id'       => 'duru-sans',
       'name'     => "Duru Sans",
       'label'    => "'Duru Sans', sans-serif",
       'family'   => "Duru Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'dynalight' => array(
       'id'       => 'dynalight',
       'name'     => "Dynalight",
       'label'    => "Dynalight, display",
       'family'   => "Dynalight, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'eb-garamond' => array(
       'id'       => 'eb-garamond',
       'name'     => "EB Garamond",
       'label'    => "'EB Garamond', serif",
       'family'   => "EB Garamond, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'eagle-lake' => array(
       'id'       => 'eagle-lake',
       'name'     => "Eagle Lake",
       'label'    => "'Eagle Lake', handwriting",
       'family'   => "Eagle Lake, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'eater' => array(
       'id'       => 'eater',
       'name'     => "Eater",
       'label'    => "Eater, display",
       'family'   => "Eater, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'economica' => array(
       'id'       => 'economica',
       'name'     => "Economica",
       'label'    => "Economica, sans-serif",
       'family'   => "Economica, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'eczar' => array(
       'id'       => 'eczar',
       'name'     => "Eczar",
       'label'    => "Eczar, serif",
       'family'   => "Eczar, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'ek-mukta' => array(
       'id'       => 'ek-mukta',
       'name'     => "Ek Mukta",
       'label'    => "'Ek Mukta', sans-serif",
       'family'   => "Ek Mukta, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'el-messiri' => array(
       'id'       => 'el-messiri',
       'name'     => "El Messiri",
       'label'    => "'El Messiri', sans-serif",
       'family'   => "El Messiri, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'arabic'  => 'Arabic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'electrolize' => array(
       'id'       => 'electrolize',
       'name'     => "Electrolize",
       'label'    => "Electrolize, sans-serif",
       'family'   => "Electrolize, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'elsie' => array(
       'id'       => 'elsie',
       'name'     => "Elsie",
       'label'    => "Elsie, display",
       'family'   => "Elsie, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '900'  =>  'Black 900',
       ),
  ),

  'elsie-swash-caps' => array(
       'id'       => 'elsie-swash-caps',
       'name'     => "Elsie Swash Caps",
       'label'    => "'Elsie Swash Caps', display",
       'family'   => "Elsie Swash Caps, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '900'  =>  'Black 900',
       ),
  ),

  'emblema-one' => array(
       'id'       => 'emblema-one',
       'name'     => "Emblema One",
       'label'    => "'Emblema One', display",
       'family'   => "Emblema One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'emilys-candy' => array(
       'id'       => 'emilys-candy',
       'name'     => "Emilys Candy",
       'label'    => "'Emilys Candy', display",
       'family'   => "Emilys Candy, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'engagement' => array(
       'id'       => 'engagement',
       'name'     => "Engagement",
       'label'    => "Engagement, handwriting",
       'family'   => "Engagement, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'englebert' => array(
       'id'       => 'englebert',
       'name'     => "Englebert",
       'label'    => "Englebert, sans-serif",
       'family'   => "Englebert, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'enriqueta' => array(
       'id'       => 'enriqueta',
       'name'     => "Enriqueta",
       'label'    => "Enriqueta, serif",
       'family'   => "Enriqueta, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'erica-one' => array(
       'id'       => 'erica-one',
       'name'     => "Erica One",
       'label'    => "'Erica One', display",
       'family'   => "Erica One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'esteban' => array(
       'id'       => 'esteban',
       'name'     => "Esteban",
       'label'    => "Esteban, serif",
       'family'   => "Esteban, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'euphoria-script' => array(
       'id'       => 'euphoria-script',
       'name'     => "Euphoria Script",
       'label'    => "'Euphoria Script', handwriting",
       'family'   => "Euphoria Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ewert' => array(
       'id'       => 'ewert',
       'name'     => "Ewert",
       'label'    => "Ewert, display",
       'family'   => "Ewert, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'exo' => array(
       'id'       => 'exo',
       'name'     => "Exo",
       'label'    => "Exo, sans-serif",
       'family'   => "Exo, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'exo-2' => array(
       'id'       => 'exo-2',
       'name'     => "Exo 2",
       'label'    => "'Exo 2', sans-serif",
       'family'   => "Exo 2, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'expletus-sans' => array(
       'id'       => 'expletus-sans',
       'name'     => "Expletus Sans",
       'label'    => "'Expletus Sans', display",
       'family'   => "Expletus Sans, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'fanwood-text' => array(
       'id'       => 'fanwood-text',
       'name'     => "Fanwood Text",
       'label'    => "'Fanwood Text', serif",
       'family'   => "Fanwood Text, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'farsan' => array(
       'id'       => 'farsan',
       'name'     => "Farsan",
       'label'    => "Farsan, display",
       'family'   => "Farsan, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fascinate' => array(
       'id'       => 'fascinate',
       'name'     => "Fascinate",
       'label'    => "Fascinate, display",
       'family'   => "Fascinate, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fascinate-inline' => array(
       'id'       => 'fascinate-inline',
       'name'     => "Fascinate Inline",
       'label'    => "'Fascinate Inline', display",
       'family'   => "Fascinate Inline, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'faster-one' => array(
       'id'       => 'faster-one',
       'name'     => "Faster One",
       'label'    => "'Faster One', display",
       'family'   => "Faster One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fasthand' => array(
       'id'       => 'fasthand',
       'name'     => "Fasthand",
       'label'    => "Fasthand, serif",
       'family'   => "Fasthand, serif",
       'category' => "serif",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fauna-one' => array(
       'id'       => 'fauna-one',
       'name'     => "Fauna One",
       'label'    => "'Fauna One', serif",
       'family'   => "Fauna One, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'federant' => array(
       'id'       => 'federant',
       'name'     => "Federant",
       'label'    => "Federant, display",
       'family'   => "Federant, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'federo' => array(
       'id'       => 'federo',
       'name'     => "Federo",
       'label'    => "Federo, sans-serif",
       'family'   => "Federo, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'felipa' => array(
       'id'       => 'felipa',
       'name'     => "Felipa",
       'label'    => "Felipa, handwriting",
       'family'   => "Felipa, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fenix' => array(
       'id'       => 'fenix',
       'name'     => "Fenix",
       'label'    => "Fenix, serif",
       'family'   => "Fenix, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'finger-paint' => array(
       'id'       => 'finger-paint',
       'name'     => "Finger Paint",
       'label'    => "'Finger Paint', display",
       'family'   => "Finger Paint, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fira-mono' => array(
       'id'       => 'fira-mono',
       'name'     => "Fira Mono",
       'label'    => "'Fira Mono', monospace",
       'family'   => "Fira Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
       ),
  ),

  'fira-sans' => array(
       'id'       => 'fira-sans',
       'name'     => "Fira Sans",
       'label'    => "'Fira Sans', sans-serif",
       'family'   => "Fira Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'fira-sans-condensed' => array(
       'id'       => 'fira-sans-condensed',
       'name'     => "Fira Sans Condensed",
       'label'    => "'Fira Sans Condensed', sans-serif",
       'family'   => "Fira Sans Condensed, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'fira-sans-extra-condensed' => array(
       'id'       => 'fira-sans-extra-condensed',
       'name'     => "Fira Sans Extra Condensed",
       'label'    => "'Fira Sans Extra Condensed', sans-serif",
       'family'   => "Fira Sans Extra Condensed, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'fjalla-one' => array(
       'id'       => 'fjalla-one',
       'name'     => "Fjalla One",
       'label'    => "'Fjalla One', sans-serif",
       'family'   => "Fjalla One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fjord-one' => array(
       'id'       => 'fjord-one',
       'name'     => "Fjord One",
       'label'    => "'Fjord One', serif",
       'family'   => "Fjord One, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'flamenco' => array(
       'id'       => 'flamenco',
       'name'     => "Flamenco",
       'label'    => "Flamenco, display",
       'family'   => "Flamenco, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
       ),
  ),

  'flavors' => array(
       'id'       => 'flavors',
       'name'     => "Flavors",
       'label'    => "Flavors, display",
       'family'   => "Flavors, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fondamento' => array(
       'id'       => 'fondamento',
       'name'     => "Fondamento",
       'label'    => "Fondamento, handwriting",
       'family'   => "Fondamento, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'fontdiner-swanky' => array(
       'id'       => 'fontdiner-swanky',
       'name'     => "Fontdiner Swanky",
       'label'    => "'Fontdiner Swanky', display",
       'family'   => "Fontdiner Swanky, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'forum' => array(
       'id'       => 'forum',
       'name'     => "Forum",
       'label'    => "Forum, display",
       'family'   => "Forum, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'francois-one' => array(
       'id'       => 'francois-one',
       'name'     => "Francois One",
       'label'    => "'Francois One', sans-serif",
       'family'   => "Francois One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'frank-ruhl-libre' => array(
       'id'       => 'frank-ruhl-libre',
       'name'     => "Frank Ruhl Libre",
       'label'    => "'Frank Ruhl Libre', sans-serif",
       'family'   => "Frank Ruhl Libre, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'freckle-face' => array(
       'id'       => 'freckle-face',
       'name'     => "Freckle Face",
       'label'    => "'Freckle Face', display",
       'family'   => "Freckle Face, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fredericka-the-great' => array(
       'id'       => 'fredericka-the-great',
       'name'     => "Fredericka the Great",
       'label'    => "'Fredericka the Great', display",
       'family'   => "Fredericka the Great, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fredoka-one' => array(
       'id'       => 'fredoka-one',
       'name'     => "Fredoka One",
       'label'    => "'Fredoka One', display",
       'family'   => "Fredoka One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'freehand' => array(
       'id'       => 'freehand',
       'name'     => "Freehand",
       'label'    => "Freehand, display",
       'family'   => "Freehand, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fresca' => array(
       'id'       => 'fresca',
       'name'     => "Fresca",
       'label'    => "Fresca, sans-serif",
       'family'   => "Fresca, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'frijole' => array(
       'id'       => 'frijole',
       'name'     => "Frijole",
       'label'    => "Frijole, display",
       'family'   => "Frijole, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fruktur' => array(
       'id'       => 'fruktur',
       'name'     => "Fruktur",
       'label'    => "Fruktur, display",
       'family'   => "Fruktur, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'fugaz-one' => array(
       'id'       => 'fugaz-one',
       'name'     => "Fugaz One",
       'label'    => "'Fugaz One', display",
       'family'   => "Fugaz One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gfs-didot' => array(
       'id'       => 'gfs-didot',
       'name'     => "GFS Didot",
       'label'    => "'GFS Didot', serif",
       'family'   => "GFS Didot, serif",
       'category' => "serif",
       'subsets'  => array(
           'greek'  => 'Greek',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gfs-neohellenic' => array(
       'id'       => 'gfs-neohellenic',
       'name'     => "GFS Neohellenic",
       'label'    => "'GFS Neohellenic', sans-serif",
       'family'   => "GFS Neohellenic, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'gabriela' => array(
       'id'       => 'gabriela',
       'name'     => "Gabriela",
       'label'    => "Gabriela, serif",
       'family'   => "Gabriela, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gafata' => array(
       'id'       => 'gafata',
       'name'     => "Gafata",
       'label'    => "Gafata, sans-serif",
       'family'   => "Gafata, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'galada' => array(
       'id'       => 'galada',
       'name'     => "Galada",
       'label'    => "Galada, display",
       'family'   => "Galada, cursive",
       'category' => "display",
       'subsets'  => array(
           'bengali'  => 'Bengali',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'galdeano' => array(
       'id'       => 'galdeano',
       'name'     => "Galdeano",
       'label'    => "Galdeano, sans-serif",
       'family'   => "Galdeano, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'galindo' => array(
       'id'       => 'galindo',
       'name'     => "Galindo",
       'label'    => "Galindo, display",
       'family'   => "Galindo, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gentium-basic' => array(
       'id'       => 'gentium-basic',
       'name'     => "Gentium Basic",
       'label'    => "'Gentium Basic', serif",
       'family'   => "Gentium Basic, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'gentium-book-basic' => array(
       'id'       => 'gentium-book-basic',
       'name'     => "Gentium Book Basic",
       'label'    => "'Gentium Book Basic', serif",
       'family'   => "Gentium Book Basic, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'geo' => array(
       'id'       => 'geo',
       'name'     => "Geo",
       'label'    => "Geo, sans-serif",
       'family'   => "Geo, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'geostar' => array(
       'id'       => 'geostar',
       'name'     => "Geostar",
       'label'    => "Geostar, display",
       'family'   => "Geostar, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'geostar-fill' => array(
       'id'       => 'geostar-fill',
       'name'     => "Geostar Fill",
       'label'    => "'Geostar Fill', display",
       'family'   => "Geostar Fill, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'germania-one' => array(
       'id'       => 'germania-one',
       'name'     => "Germania One",
       'label'    => "'Germania One', display",
       'family'   => "Germania One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gidugu' => array(
       'id'       => 'gidugu',
       'name'     => "Gidugu",
       'label'    => "Gidugu, sans-serif",
       'family'   => "Gidugu, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gilda-display' => array(
       'id'       => 'gilda-display',
       'name'     => "Gilda Display",
       'label'    => "'Gilda Display', serif",
       'family'   => "Gilda Display, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'give-you-glory' => array(
       'id'       => 'give-you-glory',
       'name'     => "Give You Glory",
       'label'    => "'Give You Glory', handwriting",
       'family'   => "Give You Glory, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'glass-antiqua' => array(
       'id'       => 'glass-antiqua',
       'name'     => "Glass Antiqua",
       'label'    => "'Glass Antiqua', display",
       'family'   => "Glass Antiqua, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'glegoo' => array(
       'id'       => 'glegoo',
       'name'     => "Glegoo",
       'label'    => "Glegoo, serif",
       'family'   => "Glegoo, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'gloria-hallelujah' => array(
       'id'       => 'gloria-hallelujah',
       'name'     => "Gloria Hallelujah",
       'label'    => "'Gloria Hallelujah', handwriting",
       'family'   => "Gloria Hallelujah, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'goblin-one' => array(
       'id'       => 'goblin-one',
       'name'     => "Goblin One",
       'label'    => "'Goblin One', display",
       'family'   => "Goblin One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gochi-hand' => array(
       'id'       => 'gochi-hand',
       'name'     => "Gochi Hand",
       'label'    => "'Gochi Hand', handwriting",
       'family'   => "Gochi Hand, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gorditas' => array(
       'id'       => 'gorditas',
       'name'     => "Gorditas",
       'label'    => "Gorditas, display",
       'family'   => "Gorditas, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'goudy-bookletter-1911' => array(
       'id'       => 'goudy-bookletter-1911',
       'name'     => "Goudy Bookletter 1911",
       'label'    => "'Goudy Bookletter 1911', serif",
       'family'   => "Goudy Bookletter 1911, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'graduate' => array(
       'id'       => 'graduate',
       'name'     => "Graduate",
       'label'    => "Graduate, display",
       'family'   => "Graduate, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'grand-hotel' => array(
       'id'       => 'grand-hotel',
       'name'     => "Grand Hotel",
       'label'    => "'Grand Hotel', handwriting",
       'family'   => "Grand Hotel, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gravitas-one' => array(
       'id'       => 'gravitas-one',
       'name'     => "Gravitas One",
       'label'    => "'Gravitas One', display",
       'family'   => "Gravitas One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'great-vibes' => array(
       'id'       => 'great-vibes',
       'name'     => "Great Vibes",
       'label'    => "'Great Vibes', handwriting",
       'family'   => "Great Vibes, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'griffy' => array(
       'id'       => 'griffy',
       'name'     => "Griffy",
       'label'    => "Griffy, display",
       'family'   => "Griffy, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gruppo' => array(
       'id'       => 'gruppo',
       'name'     => "Gruppo",
       'label'    => "Gruppo, display",
       'family'   => "Gruppo, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'gudea' => array(
       'id'       => 'gudea',
       'name'     => "Gudea",
       'label'    => "Gudea, sans-serif",
       'family'   => "Gudea, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'gurajada' => array(
       'id'       => 'gurajada',
       'name'     => "Gurajada",
       'label'    => "Gurajada, serif",
       'family'   => "Gurajada, serif",
       'category' => "serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'habibi' => array(
       'id'       => 'habibi',
       'name'     => "Habibi",
       'label'    => "Habibi, serif",
       'family'   => "Habibi, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'halant' => array(
       'id'       => 'halant',
       'name'     => "Halant",
       'label'    => "Halant, serif",
       'family'   => "Halant, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'hammersmith-one' => array(
       'id'       => 'hammersmith-one',
       'name'     => "Hammersmith One",
       'label'    => "'Hammersmith One', sans-serif",
       'family'   => "Hammersmith One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'hanalei' => array(
       'id'       => 'hanalei',
       'name'     => "Hanalei",
       'label'    => "Hanalei, display",
       'family'   => "Hanalei, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'hanalei-fill' => array(
       'id'       => 'hanalei-fill',
       'name'     => "Hanalei Fill",
       'label'    => "'Hanalei Fill', display",
       'family'   => "Hanalei Fill, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'handlee' => array(
       'id'       => 'handlee',
       'name'     => "Handlee",
       'label'    => "Handlee, handwriting",
       'family'   => "Handlee, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'hanuman' => array(
       'id'       => 'hanuman',
       'name'     => "Hanuman",
       'label'    => "Hanuman, serif",
       'family'   => "Hanuman, serif",
       'category' => "serif",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'happy-monkey' => array(
       'id'       => 'happy-monkey',
       'name'     => "Happy Monkey",
       'label'    => "'Happy Monkey', display",
       'family'   => "Happy Monkey, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'harmattan' => array(
       'id'       => 'harmattan',
       'name'     => "Harmattan",
       'label'    => "Harmattan, sans-serif",
       'family'   => "Harmattan, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'headland-one' => array(
       'id'       => 'headland-one',
       'name'     => "Headland One",
       'label'    => "'Headland One', serif",
       'family'   => "Headland One, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'heebo' => array(
       'id'       => 'heebo',
       'name'     => "Heebo",
       'label'    => "Heebo, sans-serif",
       'family'   => "Heebo, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'hebrew'  => 'Hebrew',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'henny-penny' => array(
       'id'       => 'henny-penny',
       'name'     => "Henny Penny",
       'label'    => "'Henny Penny', display",
       'family'   => "Henny Penny, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'herr-von-muellerhoff' => array(
       'id'       => 'herr-von-muellerhoff',
       'name'     => "Herr Von Muellerhoff",
       'label'    => "'Herr Von Muellerhoff', handwriting",
       'family'   => "Herr Von Muellerhoff, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'hind' => array(
       'id'       => 'hind',
       'name'     => "Hind",
       'label'    => "Hind, sans-serif",
       'family'   => "Hind, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'hind-guntur' => array(
       'id'       => 'hind-guntur',
       'name'     => "Hind Guntur",
       'label'    => "'Hind Guntur', sans-serif",
       'family'   => "Hind Guntur, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'hind-madurai' => array(
       'id'       => 'hind-madurai',
       'name'     => "Hind Madurai",
       'label'    => "'Hind Madurai', sans-serif",
       'family'   => "Hind Madurai, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'tamil'  => 'Tamil',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'hind-siliguri' => array(
       'id'       => 'hind-siliguri',
       'name'     => "Hind Siliguri",
       'label'    => "'Hind Siliguri', sans-serif",
       'family'   => "Hind Siliguri, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'bengali'  => 'Bengali',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'hind-vadodara' => array(
       'id'       => 'hind-vadodara',
       'name'     => "Hind Vadodara",
       'label'    => "'Hind Vadodara', sans-serif",
       'family'   => "Hind Vadodara, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'holtwood-one-sc' => array(
       'id'       => 'holtwood-one-sc',
       'name'     => "Holtwood One SC",
       'label'    => "'Holtwood One SC', serif",
       'family'   => "Holtwood One SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'homemade-apple' => array(
       'id'       => 'homemade-apple',
       'name'     => "Homemade Apple",
       'label'    => "'Homemade Apple', handwriting",
       'family'   => "Homemade Apple, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'homenaje' => array(
       'id'       => 'homenaje',
       'name'     => "Homenaje",
       'label'    => "Homenaje, sans-serif",
       'family'   => "Homenaje, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'im-fell-dw-pica' => array(
       'id'       => 'im-fell-dw-pica',
       'name'     => "IM Fell DW Pica",
       'label'    => "'IM Fell DW Pica', serif",
       'family'   => "IM Fell DW Pica, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'im-fell-dw-pica-sc' => array(
       'id'       => 'im-fell-dw-pica-sc',
       'name'     => "IM Fell DW Pica SC",
       'label'    => "'IM Fell DW Pica SC', serif",
       'family'   => "IM Fell DW Pica SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'im-fell-double-pica' => array(
       'id'       => 'im-fell-double-pica',
       'name'     => "IM Fell Double Pica",
       'label'    => "'IM Fell Double Pica', serif",
       'family'   => "IM Fell Double Pica, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'im-fell-double-pica-sc' => array(
       'id'       => 'im-fell-double-pica-sc',
       'name'     => "IM Fell Double Pica SC",
       'label'    => "'IM Fell Double Pica SC', serif",
       'family'   => "IM Fell Double Pica SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'im-fell-english' => array(
       'id'       => 'im-fell-english',
       'name'     => "IM Fell English",
       'label'    => "'IM Fell English', serif",
       'family'   => "IM Fell English, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'im-fell-english-sc' => array(
       'id'       => 'im-fell-english-sc',
       'name'     => "IM Fell English SC",
       'label'    => "'IM Fell English SC', serif",
       'family'   => "IM Fell English SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'im-fell-french-canon' => array(
       'id'       => 'im-fell-french-canon',
       'name'     => "IM Fell French Canon",
       'label'    => "'IM Fell French Canon', serif",
       'family'   => "IM Fell French Canon, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'im-fell-french-canon-sc' => array(
       'id'       => 'im-fell-french-canon-sc',
       'name'     => "IM Fell French Canon SC",
       'label'    => "'IM Fell French Canon SC', serif",
       'family'   => "IM Fell French Canon SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'im-fell-great-primer' => array(
       'id'       => 'im-fell-great-primer',
       'name'     => "IM Fell Great Primer",
       'label'    => "'IM Fell Great Primer', serif",
       'family'   => "IM Fell Great Primer, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'im-fell-great-primer-sc' => array(
       'id'       => 'im-fell-great-primer-sc',
       'name'     => "IM Fell Great Primer SC",
       'label'    => "'IM Fell Great Primer SC', serif",
       'family'   => "IM Fell Great Primer SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'iceberg' => array(
       'id'       => 'iceberg',
       'name'     => "Iceberg",
       'label'    => "Iceberg, display",
       'family'   => "Iceberg, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'iceland' => array(
       'id'       => 'iceland',
       'name'     => "Iceland",
       'label'    => "Iceland, display",
       'family'   => "Iceland, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'imprima' => array(
       'id'       => 'imprima',
       'name'     => "Imprima",
       'label'    => "Imprima, sans-serif",
       'family'   => "Imprima, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'inconsolata' => array(
       'id'       => 'inconsolata',
       'name'     => "Inconsolata",
       'label'    => "Inconsolata, monospace",
       'family'   => "Inconsolata, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'inder' => array(
       'id'       => 'inder',
       'name'     => "Inder",
       'label'    => "Inder, sans-serif",
       'family'   => "Inder, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'indie-flower' => array(
       'id'       => 'indie-flower',
       'name'     => "Indie Flower",
       'label'    => "'Indie Flower', handwriting",
       'family'   => "Indie Flower, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'inika' => array(
       'id'       => 'inika',
       'name'     => "Inika",
       'label'    => "Inika, serif",
       'family'   => "Inika, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'inknut-antiqua' => array(
       'id'       => 'inknut-antiqua',
       'name'     => "Inknut Antiqua",
       'label'    => "'Inknut Antiqua', serif",
       'family'   => "Inknut Antiqua, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'irish-grover' => array(
       'id'       => 'irish-grover',
       'name'     => "Irish Grover",
       'label'    => "'Irish Grover', display",
       'family'   => "Irish Grover, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'istok-web' => array(
       'id'       => 'istok-web',
       'name'     => "Istok Web",
       'label'    => "'Istok Web', sans-serif",
       'family'   => "Istok Web, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'italiana' => array(
       'id'       => 'italiana',
       'name'     => "Italiana",
       'label'    => "Italiana, serif",
       'family'   => "Italiana, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'italianno' => array(
       'id'       => 'italianno',
       'name'     => "Italianno",
       'label'    => "Italianno, handwriting",
       'family'   => "Italianno, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'itim' => array(
       'id'       => 'itim',
       'name'     => "Itim",
       'label'    => "Itim, handwriting",
       'family'   => "Itim, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'jacques-francois' => array(
       'id'       => 'jacques-francois',
       'name'     => "Jacques Francois",
       'label'    => "'Jacques Francois', serif",
       'family'   => "Jacques Francois, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'jacques-francois-shadow' => array(
       'id'       => 'jacques-francois-shadow',
       'name'     => "Jacques Francois Shadow",
       'label'    => "'Jacques Francois Shadow', display",
       'family'   => "Jacques Francois Shadow, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'jaldi' => array(
       'id'       => 'jaldi',
       'name'     => "Jaldi",
       'label'    => "Jaldi, sans-serif",
       'family'   => "Jaldi, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'jim-nightshade' => array(
       'id'       => 'jim-nightshade',
       'name'     => "Jim Nightshade",
       'label'    => "'Jim Nightshade', handwriting",
       'family'   => "Jim Nightshade, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'jockey-one' => array(
       'id'       => 'jockey-one',
       'name'     => "Jockey One",
       'label'    => "'Jockey One', sans-serif",
       'family'   => "Jockey One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'jolly-lodger' => array(
       'id'       => 'jolly-lodger',
       'name'     => "Jolly Lodger",
       'label'    => "'Jolly Lodger', display",
       'family'   => "Jolly Lodger, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'jomhuria' => array(
       'id'       => 'jomhuria',
       'name'     => "Jomhuria",
       'label'    => "Jomhuria, display",
       'family'   => "Jomhuria, cursive",
       'category' => "display",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'josefin-sans' => array(
       'id'       => 'josefin-sans',
       'name'     => "Josefin Sans",
       'label'    => "'Josefin Sans', sans-serif",
       'family'   => "Josefin Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '100i'  =>  'Thin 100 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'josefin-slab' => array(
       'id'       => 'josefin-slab',
       'name'     => "Josefin Slab",
       'label'    => "'Josefin Slab', serif",
       'family'   => "Josefin Slab, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '100i'  =>  'Thin 100 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'joti-one' => array(
       'id'       => 'joti-one',
       'name'     => "Joti One",
       'label'    => "'Joti One', display",
       'family'   => "Joti One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'judson' => array(
       'id'       => 'judson',
       'name'     => "Judson",
       'label'    => "Judson, serif",
       'family'   => "Judson, serif",
       'category' => "serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'julee' => array(
       'id'       => 'julee',
       'name'     => "Julee",
       'label'    => "Julee, handwriting",
       'family'   => "Julee, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'julius-sans-one' => array(
       'id'       => 'julius-sans-one',
       'name'     => "Julius Sans One",
       'label'    => "'Julius Sans One', sans-serif",
       'family'   => "Julius Sans One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'junge' => array(
       'id'       => 'junge',
       'name'     => "Junge",
       'label'    => "Junge, serif",
       'family'   => "Junge, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'jura' => array(
       'id'       => 'jura',
       'name'     => "Jura",
       'label'    => "Jura, sans-serif",
       'family'   => "Jura, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
       ),
  ),

  'just-another-hand' => array(
       'id'       => 'just-another-hand',
       'name'     => "Just Another Hand",
       'label'    => "'Just Another Hand', handwriting",
       'family'   => "Just Another Hand, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'just-me-again-down-here' => array(
       'id'       => 'just-me-again-down-here',
       'name'     => "Just Me Again Down Here",
       'label'    => "'Just Me Again Down Here', handwriting",
       'family'   => "Just Me Again Down Here, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kadwa' => array(
       'id'       => 'kadwa',
       'name'     => "Kadwa",
       'label'    => "Kadwa, serif",
       'family'   => "Kadwa, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'kalam' => array(
       'id'       => 'kalam',
       'name'     => "Kalam",
       'label'    => "Kalam, handwriting",
       'family'   => "Kalam, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'kameron' => array(
       'id'       => 'kameron',
       'name'     => "Kameron",
       'label'    => "Kameron, serif",
       'family'   => "Kameron, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'kanit' => array(
       'id'       => 'kanit',
       'name'     => "Kanit",
       'label'    => "Kanit, sans-serif",
       'family'   => "Kanit, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'kantumruy' => array(
       'id'       => 'kantumruy',
       'name'     => "Kantumruy",
       'label'    => "Kantumruy, sans-serif",
       'family'   => "Kantumruy, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'karla' => array(
       'id'       => 'karla',
       'name'     => "Karla",
       'label'    => "Karla, sans-serif",
       'family'   => "Karla, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'karma' => array(
       'id'       => 'karma',
       'name'     => "Karma",
       'label'    => "Karma, serif",
       'family'   => "Karma, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'katibeh' => array(
       'id'       => 'katibeh',
       'name'     => "Katibeh",
       'label'    => "Katibeh, display",
       'family'   => "Katibeh, cursive",
       'category' => "display",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kaushan-script' => array(
       'id'       => 'kaushan-script',
       'name'     => "Kaushan Script",
       'label'    => "'Kaushan Script', handwriting",
       'family'   => "Kaushan Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kavivanar' => array(
       'id'       => 'kavivanar',
       'name'     => "Kavivanar",
       'label'    => "Kavivanar, handwriting",
       'family'   => "Kavivanar, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'tamil'  => 'Tamil',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kavoon' => array(
       'id'       => 'kavoon',
       'name'     => "Kavoon",
       'label'    => "Kavoon, display",
       'family'   => "Kavoon, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kdam-thmor' => array(
       'id'       => 'kdam-thmor',
       'name'     => "Kdam Thmor",
       'label'    => "'Kdam Thmor', display",
       'family'   => "Kdam Thmor, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'keania-one' => array(
       'id'       => 'keania-one',
       'name'     => "Keania One",
       'label'    => "'Keania One', display",
       'family'   => "Keania One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kelly-slab' => array(
       'id'       => 'kelly-slab',
       'name'     => "Kelly Slab",
       'label'    => "'Kelly Slab', display",
       'family'   => "Kelly Slab, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kenia' => array(
       'id'       => 'kenia',
       'name'     => "Kenia",
       'label'    => "Kenia, display",
       'family'   => "Kenia, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'khand' => array(
       'id'       => 'khand',
       'name'     => "Khand",
       'label'    => "Khand, sans-serif",
       'family'   => "Khand, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'khmer' => array(
       'id'       => 'khmer',
       'name'     => "Khmer",
       'label'    => "Khmer, display",
       'family'   => "Khmer, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'khula' => array(
       'id'       => 'khula',
       'name'     => "Khula",
       'label'    => "Khula, sans-serif",
       'family'   => "Khula, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'kite-one' => array(
       'id'       => 'kite-one',
       'name'     => "Kite One",
       'label'    => "'Kite One', sans-serif",
       'family'   => "Kite One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'knewave' => array(
       'id'       => 'knewave',
       'name'     => "Knewave",
       'label'    => "Knewave, display",
       'family'   => "Knewave, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kotta-one' => array(
       'id'       => 'kotta-one',
       'name'     => "Kotta One",
       'label'    => "'Kotta One', serif",
       'family'   => "Kotta One, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'koulen' => array(
       'id'       => 'koulen',
       'name'     => "Koulen",
       'label'    => "Koulen, display",
       'family'   => "Koulen, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kranky' => array(
       'id'       => 'kranky',
       'name'     => "Kranky",
       'label'    => "Kranky, display",
       'family'   => "Kranky, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kreon' => array(
       'id'       => 'kreon',
       'name'     => "Kreon",
       'label'    => "Kreon, serif",
       'family'   => "Kreon, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'kristi' => array(
       'id'       => 'kristi',
       'name'     => "Kristi",
       'label'    => "Kristi, handwriting",
       'family'   => "Kristi, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'krona-one' => array(
       'id'       => 'krona-one',
       'name'     => "Krona One",
       'label'    => "'Krona One', sans-serif",
       'family'   => "Krona One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kumar-one' => array(
       'id'       => 'kumar-one',
       'name'     => "Kumar One",
       'label'    => "'Kumar One', display",
       'family'   => "Kumar One, cursive",
       'category' => "display",
       'subsets'  => array(
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kumar-one-outline' => array(
       'id'       => 'kumar-one-outline',
       'name'     => "Kumar One Outline",
       'label'    => "'Kumar One Outline', display",
       'family'   => "Kumar One Outline, cursive",
       'category' => "display",
       'subsets'  => array(
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'kurale' => array(
       'id'       => 'kurale',
       'name'     => "Kurale",
       'label'    => "Kurale, serif",
       'family'   => "Kurale, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'la-belle-aurore' => array(
       'id'       => 'la-belle-aurore',
       'name'     => "La Belle Aurore",
       'label'    => "'La Belle Aurore', handwriting",
       'family'   => "La Belle Aurore, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'laila' => array(
       'id'       => 'laila',
       'name'     => "Laila",
       'label'    => "Laila, serif",
       'family'   => "Laila, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'lakki-reddy' => array(
       'id'       => 'lakki-reddy',
       'name'     => "Lakki Reddy",
       'label'    => "'Lakki Reddy', handwriting",
       'family'   => "Lakki Reddy, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lalezar' => array(
       'id'       => 'lalezar',
       'name'     => "Lalezar",
       'label'    => "Lalezar, display",
       'family'   => "Lalezar, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'arabic'  => 'Arabic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lancelot' => array(
       'id'       => 'lancelot',
       'name'     => "Lancelot",
       'label'    => "Lancelot, display",
       'family'   => "Lancelot, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lateef' => array(
       'id'       => 'lateef',
       'name'     => "Lateef",
       'label'    => "Lateef, handwriting",
       'family'   => "Lateef, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lato' => array(
       'id'       => 'lato',
       'name'     => "Lato",
       'label'    => "Lato, sans-serif",
       'family'   => "Lato, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'league-script' => array(
       'id'       => 'league-script',
       'name'     => "League Script",
       'label'    => "'League Script', handwriting",
       'family'   => "League Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'leckerli-one' => array(
       'id'       => 'leckerli-one',
       'name'     => "Leckerli One",
       'label'    => "'Leckerli One', handwriting",
       'family'   => "Leckerli One, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ledger' => array(
       'id'       => 'ledger',
       'name'     => "Ledger",
       'label'    => "Ledger, serif",
       'family'   => "Ledger, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lekton' => array(
       'id'       => 'lekton',
       'name'     => "Lekton",
       'label'    => "Lekton, sans-serif",
       'family'   => "Lekton, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'lemon' => array(
       'id'       => 'lemon',
       'name'     => "Lemon",
       'label'    => "Lemon, display",
       'family'   => "Lemon, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lemonada' => array(
       'id'       => 'lemonada',
       'name'     => "Lemonada",
       'label'    => "Lemonada, display",
       'family'   => "Lemonada, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'arabic'  => 'Arabic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'libre-baskerville' => array(
       'id'       => 'libre-baskerville',
       'name'     => "Libre Baskerville",
       'label'    => "'Libre Baskerville', serif",
       'family'   => "Libre Baskerville, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'libre-franklin' => array(
       'id'       => 'libre-franklin',
       'name'     => "Libre Franklin",
       'label'    => "'Libre Franklin', sans-serif",
       'family'   => "Libre Franklin, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'life-savers' => array(
       'id'       => 'life-savers',
       'name'     => "Life Savers",
       'label'    => "'Life Savers', display",
       'family'   => "Life Savers, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'lilita-one' => array(
       'id'       => 'lilita-one',
       'name'     => "Lilita One",
       'label'    => "'Lilita One', display",
       'family'   => "Lilita One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lily-script-one' => array(
       'id'       => 'lily-script-one',
       'name'     => "Lily Script One",
       'label'    => "'Lily Script One', display",
       'family'   => "Lily Script One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'limelight' => array(
       'id'       => 'limelight',
       'name'     => "Limelight",
       'label'    => "Limelight, display",
       'family'   => "Limelight, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'linden-hill' => array(
       'id'       => 'linden-hill',
       'name'     => "Linden Hill",
       'label'    => "'Linden Hill', serif",
       'family'   => "Linden Hill, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'lobster' => array(
       'id'       => 'lobster',
       'name'     => "Lobster",
       'label'    => "Lobster, display",
       'family'   => "Lobster, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lobster-two' => array(
       'id'       => 'lobster-two',
       'name'     => "Lobster Two",
       'label'    => "'Lobster Two', display",
       'family'   => "Lobster Two, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'londrina-outline' => array(
       'id'       => 'londrina-outline',
       'name'     => "Londrina Outline",
       'label'    => "'Londrina Outline', display",
       'family'   => "Londrina Outline, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'londrina-shadow' => array(
       'id'       => 'londrina-shadow',
       'name'     => "Londrina Shadow",
       'label'    => "'Londrina Shadow', display",
       'family'   => "Londrina Shadow, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'londrina-sketch' => array(
       'id'       => 'londrina-sketch',
       'name'     => "Londrina Sketch",
       'label'    => "'Londrina Sketch', display",
       'family'   => "Londrina Sketch, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'londrina-solid' => array(
       'id'       => 'londrina-solid',
       'name'     => "Londrina Solid",
       'label'    => "'Londrina Solid', display",
       'family'   => "Londrina Solid, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lora' => array(
       'id'       => 'lora',
       'name'     => "Lora",
       'label'    => "Lora, serif",
       'family'   => "Lora, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'love-ya-like-a-sister' => array(
       'id'       => 'love-ya-like-a-sister',
       'name'     => "Love Ya Like A Sister",
       'label'    => "'Love Ya Like A Sister', display",
       'family'   => "Love Ya Like A Sister, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'loved-by-the-king' => array(
       'id'       => 'loved-by-the-king',
       'name'     => "Loved by the King",
       'label'    => "'Loved by the King', handwriting",
       'family'   => "Loved by the King, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lovers-quarrel' => array(
       'id'       => 'lovers-quarrel',
       'name'     => "Lovers Quarrel",
       'label'    => "'Lovers Quarrel', handwriting",
       'family'   => "Lovers Quarrel, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'luckiest-guy' => array(
       'id'       => 'luckiest-guy',
       'name'     => "Luckiest Guy",
       'label'    => "'Luckiest Guy', display",
       'family'   => "Luckiest Guy, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'lusitana' => array(
       'id'       => 'lusitana',
       'name'     => "Lusitana",
       'label'    => "Lusitana, serif",
       'family'   => "Lusitana, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'lustria' => array(
       'id'       => 'lustria',
       'name'     => "Lustria",
       'label'    => "Lustria, serif",
       'family'   => "Lustria, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'macondo' => array(
       'id'       => 'macondo',
       'name'     => "Macondo",
       'label'    => "Macondo, display",
       'family'   => "Macondo, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'macondo-swash-caps' => array(
       'id'       => 'macondo-swash-caps',
       'name'     => "Macondo Swash Caps",
       'label'    => "'Macondo Swash Caps', display",
       'family'   => "Macondo Swash Caps, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mada' => array(
       'id'       => 'mada',
       'name'     => "Mada",
       'label'    => "Mada, sans-serif",
       'family'   => "Mada, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '900'  =>  'Black 900',
       ),
  ),

  'magra' => array(
       'id'       => 'magra',
       'name'     => "Magra",
       'label'    => "Magra, sans-serif",
       'family'   => "Magra, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'maiden-orange' => array(
       'id'       => 'maiden-orange',
       'name'     => "Maiden Orange",
       'label'    => "'Maiden Orange', display",
       'family'   => "Maiden Orange, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'maitree' => array(
       'id'       => 'maitree',
       'name'     => "Maitree",
       'label'    => "Maitree, serif",
       'family'   => "Maitree, serif",
       'category' => "serif",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'mako' => array(
       'id'       => 'mako',
       'name'     => "Mako",
       'label'    => "Mako, sans-serif",
       'family'   => "Mako, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mallanna' => array(
       'id'       => 'mallanna',
       'name'     => "Mallanna",
       'label'    => "Mallanna, sans-serif",
       'family'   => "Mallanna, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mandali' => array(
       'id'       => 'mandali',
       'name'     => "Mandali",
       'label'    => "Mandali, sans-serif",
       'family'   => "Mandali, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'marcellus' => array(
       'id'       => 'marcellus',
       'name'     => "Marcellus",
       'label'    => "Marcellus, serif",
       'family'   => "Marcellus, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'marcellus-sc' => array(
       'id'       => 'marcellus-sc',
       'name'     => "Marcellus SC",
       'label'    => "'Marcellus SC', serif",
       'family'   => "Marcellus SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'marck-script' => array(
       'id'       => 'marck-script',
       'name'     => "Marck Script",
       'label'    => "'Marck Script', handwriting",
       'family'   => "Marck Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'margarine' => array(
       'id'       => 'margarine',
       'name'     => "Margarine",
       'label'    => "Margarine, display",
       'family'   => "Margarine, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'marko-one' => array(
       'id'       => 'marko-one',
       'name'     => "Marko One",
       'label'    => "'Marko One', serif",
       'family'   => "Marko One, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'marmelad' => array(
       'id'       => 'marmelad',
       'name'     => "Marmelad",
       'label'    => "Marmelad, sans-serif",
       'family'   => "Marmelad, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'martel' => array(
       'id'       => 'martel',
       'name'     => "Martel",
       'label'    => "Martel, serif",
       'family'   => "Martel, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'martel-sans' => array(
       'id'       => 'martel-sans',
       'name'     => "Martel Sans",
       'label'    => "'Martel Sans', sans-serif",
       'family'   => "Martel Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'marvel' => array(
       'id'       => 'marvel',
       'name'     => "Marvel",
       'label'    => "Marvel, sans-serif",
       'family'   => "Marvel, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'mate' => array(
       'id'       => 'mate',
       'name'     => "Mate",
       'label'    => "Mate, serif",
       'family'   => "Mate, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'mate-sc' => array(
       'id'       => 'mate-sc',
       'name'     => "Mate SC",
       'label'    => "'Mate SC', serif",
       'family'   => "Mate SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'maven-pro' => array(
       'id'       => 'maven-pro',
       'name'     => "Maven Pro",
       'label'    => "'Maven Pro', sans-serif",
       'family'   => "Maven Pro, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'mclaren' => array(
       'id'       => 'mclaren',
       'name'     => "McLaren",
       'label'    => "McLaren, display",
       'family'   => "McLaren, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'meddon' => array(
       'id'       => 'meddon',
       'name'     => "Meddon",
       'label'    => "Meddon, handwriting",
       'family'   => "Meddon, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'medievalsharp' => array(
       'id'       => 'medievalsharp',
       'name'     => "MedievalSharp",
       'label'    => "MedievalSharp, display",
       'family'   => "MedievalSharp, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'medula-one' => array(
       'id'       => 'medula-one',
       'name'     => "Medula One",
       'label'    => "'Medula One', display",
       'family'   => "Medula One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'meera-inimai' => array(
       'id'       => 'meera-inimai',
       'name'     => "Meera Inimai",
       'label'    => "'Meera Inimai', sans-serif",
       'family'   => "Meera Inimai, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'tamil'  => 'Tamil',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'megrim' => array(
       'id'       => 'megrim',
       'name'     => "Megrim",
       'label'    => "Megrim, display",
       'family'   => "Megrim, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'meie-script' => array(
       'id'       => 'meie-script',
       'name'     => "Meie Script",
       'label'    => "'Meie Script', handwriting",
       'family'   => "Meie Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'merienda' => array(
       'id'       => 'merienda',
       'name'     => "Merienda",
       'label'    => "Merienda, handwriting",
       'family'   => "Merienda, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'merienda-one' => array(
       'id'       => 'merienda-one',
       'name'     => "Merienda One",
       'label'    => "'Merienda One', handwriting",
       'family'   => "Merienda One, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'merriweather' => array(
       'id'       => 'merriweather',
       'name'     => "Merriweather",
       'label'    => "Merriweather, serif",
       'family'   => "Merriweather, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'merriweather-sans' => array(
       'id'       => 'merriweather-sans',
       'name'     => "Merriweather Sans",
       'label'    => "'Merriweather Sans', sans-serif",
       'family'   => "Merriweather Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
       ),
  ),

  'metal' => array(
       'id'       => 'metal',
       'name'     => "Metal",
       'label'    => "Metal, display",
       'family'   => "Metal, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'metal-mania' => array(
       'id'       => 'metal-mania',
       'name'     => "Metal Mania",
       'label'    => "'Metal Mania', display",
       'family'   => "Metal Mania, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'metamorphous' => array(
       'id'       => 'metamorphous',
       'name'     => "Metamorphous",
       'label'    => "Metamorphous, display",
       'family'   => "Metamorphous, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'metrophobic' => array(
       'id'       => 'metrophobic',
       'name'     => "Metrophobic",
       'label'    => "Metrophobic, sans-serif",
       'family'   => "Metrophobic, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'michroma' => array(
       'id'       => 'michroma',
       'name'     => "Michroma",
       'label'    => "Michroma, sans-serif",
       'family'   => "Michroma, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'milonga' => array(
       'id'       => 'milonga',
       'name'     => "Milonga",
       'label'    => "Milonga, display",
       'family'   => "Milonga, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'miltonian' => array(
       'id'       => 'miltonian',
       'name'     => "Miltonian",
       'label'    => "Miltonian, display",
       'family'   => "Miltonian, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'miltonian-tattoo' => array(
       'id'       => 'miltonian-tattoo',
       'name'     => "Miltonian Tattoo",
       'label'    => "'Miltonian Tattoo', display",
       'family'   => "Miltonian Tattoo, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'miniver' => array(
       'id'       => 'miniver',
       'name'     => "Miniver",
       'label'    => "Miniver, display",
       'family'   => "Miniver, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'miriam-libre' => array(
       'id'       => 'miriam-libre',
       'name'     => "Miriam Libre",
       'label'    => "'Miriam Libre', sans-serif",
       'family'   => "Miriam Libre, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'mirza' => array(
       'id'       => 'mirza',
       'name'     => "Mirza",
       'label'    => "Mirza, display",
       'family'   => "Mirza, cursive",
       'category' => "display",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'miss-fajardose' => array(
       'id'       => 'miss-fajardose',
       'name'     => "Miss Fajardose",
       'label'    => "'Miss Fajardose', handwriting",
       'family'   => "Miss Fajardose, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mitr' => array(
       'id'       => 'mitr',
       'name'     => "Mitr",
       'label'    => "Mitr, sans-serif",
       'family'   => "Mitr, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'modak' => array(
       'id'       => 'modak',
       'name'     => "Modak",
       'label'    => "Modak, display",
       'family'   => "Modak, cursive",
       'category' => "display",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'modern-antiqua' => array(
       'id'       => 'modern-antiqua',
       'name'     => "Modern Antiqua",
       'label'    => "'Modern Antiqua', display",
       'family'   => "Modern Antiqua, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mogra' => array(
       'id'       => 'mogra',
       'name'     => "Mogra",
       'label'    => "Mogra, display",
       'family'   => "Mogra, cursive",
       'category' => "display",
       'subsets'  => array(
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'molengo' => array(
       'id'       => 'molengo',
       'name'     => "Molengo",
       'label'    => "Molengo, sans-serif",
       'family'   => "Molengo, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'molle' => array(
       'id'       => 'molle',
       'name'     => "Molle",
       'label'    => "Molle, handwriting",
       'family'   => "Molle, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'monda' => array(
       'id'       => 'monda',
       'name'     => "Monda",
       'label'    => "Monda, sans-serif",
       'family'   => "Monda, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'monofett' => array(
       'id'       => 'monofett',
       'name'     => "Monofett",
       'label'    => "Monofett, display",
       'family'   => "Monofett, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'monoton' => array(
       'id'       => 'monoton',
       'name'     => "Monoton",
       'label'    => "Monoton, display",
       'family'   => "Monoton, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'monsieur-la-doulaise' => array(
       'id'       => 'monsieur-la-doulaise',
       'name'     => "Monsieur La Doulaise",
       'label'    => "'Monsieur La Doulaise', handwriting",
       'family'   => "Monsieur La Doulaise, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'montaga' => array(
       'id'       => 'montaga',
       'name'     => "Montaga",
       'label'    => "Montaga, serif",
       'family'   => "Montaga, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'montez' => array(
       'id'       => 'montez',
       'name'     => "Montez",
       'label'    => "Montez, handwriting",
       'family'   => "Montez, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'montserrat' => array(
       'id'       => 'montserrat',
       'name'     => "Montserrat",
       'label'    => "Montserrat, sans-serif",
       'family'   => "Montserrat, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'montserrat-alternates' => array(
       'id'       => 'montserrat-alternates',
       'name'     => "Montserrat Alternates",
       'label'    => "'Montserrat Alternates', sans-serif",
       'family'   => "Montserrat Alternates, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'montserrat-subrayada' => array(
       'id'       => 'montserrat-subrayada',
       'name'     => "Montserrat Subrayada",
       'label'    => "'Montserrat Subrayada', sans-serif",
       'family'   => "Montserrat Subrayada, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'moul' => array(
       'id'       => 'moul',
       'name'     => "Moul",
       'label'    => "Moul, display",
       'family'   => "Moul, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'moulpali' => array(
       'id'       => 'moulpali',
       'name'     => "Moulpali",
       'label'    => "Moulpali, display",
       'family'   => "Moulpali, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mountains-of-christmas' => array(
       'id'       => 'mountains-of-christmas',
       'name'     => "Mountains of Christmas",
       'label'    => "'Mountains of Christmas', display",
       'family'   => "Mountains of Christmas, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'mouse-memoirs' => array(
       'id'       => 'mouse-memoirs',
       'name'     => "Mouse Memoirs",
       'label'    => "'Mouse Memoirs', sans-serif",
       'family'   => "Mouse Memoirs, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mr-bedfort' => array(
       'id'       => 'mr-bedfort',
       'name'     => "Mr Bedfort",
       'label'    => "'Mr Bedfort', handwriting",
       'family'   => "Mr Bedfort, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mr-dafoe' => array(
       'id'       => 'mr-dafoe',
       'name'     => "Mr Dafoe",
       'label'    => "'Mr Dafoe', handwriting",
       'family'   => "Mr Dafoe, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mr-de-haviland' => array(
       'id'       => 'mr-de-haviland',
       'name'     => "Mr De Haviland",
       'label'    => "'Mr De Haviland', handwriting",
       'family'   => "Mr De Haviland, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mrs-saint-delafield' => array(
       'id'       => 'mrs-saint-delafield',
       'name'     => "Mrs Saint Delafield",
       'label'    => "'Mrs Saint Delafield', handwriting",
       'family'   => "Mrs Saint Delafield, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mrs-sheppards' => array(
       'id'       => 'mrs-sheppards',
       'name'     => "Mrs Sheppards",
       'label'    => "'Mrs Sheppards', handwriting",
       'family'   => "Mrs Sheppards, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'mukta-vaani' => array(
       'id'       => 'mukta-vaani',
       'name'     => "Mukta Vaani",
       'label'    => "'Mukta Vaani', sans-serif",
       'family'   => "Mukta Vaani, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'muli' => array(
       'id'       => 'muli',
       'name'     => "Muli",
       'label'    => "Muli, sans-serif",
       'family'   => "Muli, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'mystery-quest' => array(
       'id'       => 'mystery-quest',
       'name'     => "Mystery Quest",
       'label'    => "'Mystery Quest', display",
       'family'   => "Mystery Quest, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ntr' => array(
       'id'       => 'ntr',
       'name'     => "NTR",
       'label'    => "NTR, sans-serif",
       'family'   => "NTR, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'neucha' => array(
       'id'       => 'neucha',
       'name'     => "Neucha",
       'label'    => "Neucha, handwriting",
       'family'   => "Neucha, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'neuton' => array(
       'id'       => 'neuton',
       'name'     => "Neuton",
       'label'    => "Neuton, serif",
       'family'   => "Neuton, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'new-rocker' => array(
       'id'       => 'new-rocker',
       'name'     => "New Rocker",
       'label'    => "'New Rocker', display",
       'family'   => "New Rocker, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'news-cycle' => array(
       'id'       => 'news-cycle',
       'name'     => "News Cycle",
       'label'    => "'News Cycle', sans-serif",
       'family'   => "News Cycle, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'niconne' => array(
       'id'       => 'niconne',
       'name'     => "Niconne",
       'label'    => "Niconne, handwriting",
       'family'   => "Niconne, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nixie-one' => array(
       'id'       => 'nixie-one',
       'name'     => "Nixie One",
       'label'    => "'Nixie One', display",
       'family'   => "Nixie One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nobile' => array(
       'id'       => 'nobile',
       'name'     => "Nobile",
       'label'    => "Nobile, sans-serif",
       'family'   => "Nobile, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'nokora' => array(
       'id'       => 'nokora',
       'name'     => "Nokora",
       'label'    => "Nokora, serif",
       'family'   => "Nokora, serif",
       'category' => "serif",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'norican' => array(
       'id'       => 'norican',
       'name'     => "Norican",
       'label'    => "Norican, handwriting",
       'family'   => "Norican, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nosifer' => array(
       'id'       => 'nosifer',
       'name'     => "Nosifer",
       'label'    => "Nosifer, display",
       'family'   => "Nosifer, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nothing-you-could-do' => array(
       'id'       => 'nothing-you-could-do',
       'name'     => "Nothing You Could Do",
       'label'    => "'Nothing You Could Do', handwriting",
       'family'   => "Nothing You Could Do, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'noticia-text' => array(
       'id'       => 'noticia-text',
       'name'     => "Noticia Text",
       'label'    => "'Noticia Text', serif",
       'family'   => "Noticia Text, serif",
       'category' => "serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'noto-sans' => array(
       'id'       => 'noto-sans',
       'name'     => "Noto Sans",
       'label'    => "'Noto Sans', sans-serif",
       'family'   => "Noto Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'devanagari'  => 'Devanagari',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'noto-serif' => array(
       'id'       => 'noto-serif',
       'name'     => "Noto Serif",
       'label'    => "'Noto Serif', serif",
       'family'   => "Noto Serif, serif",
       'category' => "serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'nova-cut' => array(
       'id'       => 'nova-cut',
       'name'     => "Nova Cut",
       'label'    => "'Nova Cut', display",
       'family'   => "Nova Cut, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nova-flat' => array(
       'id'       => 'nova-flat',
       'name'     => "Nova Flat",
       'label'    => "'Nova Flat', display",
       'family'   => "Nova Flat, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nova-mono' => array(
       'id'       => 'nova-mono',
       'name'     => "Nova Mono",
       'label'    => "'Nova Mono', monospace",
       'family'   => "Nova Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'greek'  => 'Greek',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nova-oval' => array(
       'id'       => 'nova-oval',
       'name'     => "Nova Oval",
       'label'    => "'Nova Oval', display",
       'family'   => "Nova Oval, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nova-round' => array(
       'id'       => 'nova-round',
       'name'     => "Nova Round",
       'label'    => "'Nova Round', display",
       'family'   => "Nova Round, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nova-script' => array(
       'id'       => 'nova-script',
       'name'     => "Nova Script",
       'label'    => "'Nova Script', display",
       'family'   => "Nova Script, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nova-slim' => array(
       'id'       => 'nova-slim',
       'name'     => "Nova Slim",
       'label'    => "'Nova Slim', display",
       'family'   => "Nova Slim, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nova-square' => array(
       'id'       => 'nova-square',
       'name'     => "Nova Square",
       'label'    => "'Nova Square', display",
       'family'   => "Nova Square, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'numans' => array(
       'id'       => 'numans',
       'name'     => "Numans",
       'label'    => "Numans, sans-serif",
       'family'   => "Numans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'nunito' => array(
       'id'       => 'nunito',
       'name'     => "Nunito",
       'label'    => "Nunito, sans-serif",
       'family'   => "Nunito, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'nunito-sans' => array(
       'id'       => 'nunito-sans',
       'name'     => "Nunito Sans",
       'label'    => "'Nunito Sans', sans-serif",
       'family'   => "Nunito Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'odor-mean-chey' => array(
       'id'       => 'odor-mean-chey',
       'name'     => "Odor Mean Chey",
       'label'    => "'Odor Mean Chey', display",
       'family'   => "Odor Mean Chey, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'offside' => array(
       'id'       => 'offside',
       'name'     => "Offside",
       'label'    => "Offside, display",
       'family'   => "Offside, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'old-standard-tt' => array(
       'id'       => 'old-standard-tt',
       'name'     => "Old Standard TT",
       'label'    => "'Old Standard TT', serif",
       'family'   => "Old Standard TT, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'oldenburg' => array(
       'id'       => 'oldenburg',
       'name'     => "Oldenburg",
       'label'    => "Oldenburg, display",
       'family'   => "Oldenburg, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'oleo-script' => array(
       'id'       => 'oleo-script',
       'name'     => "Oleo Script",
       'label'    => "'Oleo Script', display",
       'family'   => "Oleo Script, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'oleo-script-swash-caps' => array(
       'id'       => 'oleo-script-swash-caps',
       'name'     => "Oleo Script Swash Caps",
       'label'    => "'Oleo Script Swash Caps', display",
       'family'   => "Oleo Script Swash Caps, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'open-sans' => array(
       'id'       => 'open-sans',
       'name'     => "Open Sans",
       'label'    => "'Open Sans', sans-serif",
       'family'   => "Open Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
       ),
  ),

  'open-sans-condensed' => array(
       'id'       => 'open-sans-condensed',
       'name'     => "Open Sans Condensed",
       'label'    => "'Open Sans Condensed', sans-serif",
       'family'   => "Open Sans Condensed, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
       ),
  ),

  'oranienbaum' => array(
       'id'       => 'oranienbaum',
       'name'     => "Oranienbaum",
       'label'    => "Oranienbaum, serif",
       'family'   => "Oranienbaum, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'orbitron' => array(
       'id'       => 'orbitron',
       'name'     => "Orbitron",
       'label'    => "Orbitron, sans-serif",
       'family'   => "Orbitron, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'oregano' => array(
       'id'       => 'oregano',
       'name'     => "Oregano",
       'label'    => "Oregano, display",
       'family'   => "Oregano, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'orienta' => array(
       'id'       => 'orienta',
       'name'     => "Orienta",
       'label'    => "Orienta, sans-serif",
       'family'   => "Orienta, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'original-surfer' => array(
       'id'       => 'original-surfer',
       'name'     => "Original Surfer",
       'label'    => "'Original Surfer', display",
       'family'   => "Original Surfer, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'oswald' => array(
       'id'       => 'oswald',
       'name'     => "Oswald",
       'label'    => "Oswald, sans-serif",
       'family'   => "Oswald, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'over-the-rainbow' => array(
       'id'       => 'over-the-rainbow',
       'name'     => "Over the Rainbow",
       'label'    => "'Over the Rainbow', handwriting",
       'family'   => "Over the Rainbow, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'overlock' => array(
       'id'       => 'overlock',
       'name'     => "Overlock",
       'label'    => "Overlock, display",
       'family'   => "Overlock, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'overlock-sc' => array(
       'id'       => 'overlock-sc',
       'name'     => "Overlock SC",
       'label'    => "'Overlock SC', display",
       'family'   => "Overlock SC, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'overpass' => array(
       'id'       => 'overpass',
       'name'     => "Overpass",
       'label'    => "Overpass, sans-serif",
       'family'   => "Overpass, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'overpass-mono' => array(
       'id'       => 'overpass-mono',
       'name'     => "Overpass Mono",
       'label'    => "'Overpass Mono', monospace",
       'family'   => "Overpass Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'ovo' => array(
       'id'       => 'ovo',
       'name'     => "Ovo",
       'label'    => "Ovo, serif",
       'family'   => "Ovo, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'oxygen' => array(
       'id'       => 'oxygen',
       'name'     => "Oxygen",
       'label'    => "Oxygen, sans-serif",
       'family'   => "Oxygen, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'oxygen-mono' => array(
       'id'       => 'oxygen-mono',
       'name'     => "Oxygen Mono",
       'label'    => "'Oxygen Mono', monospace",
       'family'   => "Oxygen Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pt-mono' => array(
       'id'       => 'pt-mono',
       'name'     => "PT Mono",
       'label'    => "'PT Mono', monospace",
       'family'   => "PT Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pt-sans' => array(
       'id'       => 'pt-sans',
       'name'     => "PT Sans",
       'label'    => "'PT Sans', sans-serif",
       'family'   => "PT Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'pt-sans-caption' => array(
       'id'       => 'pt-sans-caption',
       'name'     => "PT Sans Caption",
       'label'    => "'PT Sans Caption', sans-serif",
       'family'   => "PT Sans Caption, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'pt-sans-narrow' => array(
       'id'       => 'pt-sans-narrow',
       'name'     => "PT Sans Narrow",
       'label'    => "'PT Sans Narrow', sans-serif",
       'family'   => "PT Sans Narrow, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'pt-serif' => array(
       'id'       => 'pt-serif',
       'name'     => "PT Serif",
       'label'    => "'PT Serif', serif",
       'family'   => "PT Serif, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'pt-serif-caption' => array(
       'id'       => 'pt-serif-caption',
       'name'     => "PT Serif Caption",
       'label'    => "'PT Serif Caption', serif",
       'family'   => "PT Serif Caption, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'pacifico' => array(
       'id'       => 'pacifico',
       'name'     => "Pacifico",
       'label'    => "Pacifico, handwriting",
       'family'   => "Pacifico, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'padauk' => array(
       'id'       => 'padauk',
       'name'     => "Padauk",
       'label'    => "Padauk, sans-serif",
       'family'   => "Padauk, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'myanmar'  => 'undefined',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'palanquin' => array(
       'id'       => 'palanquin',
       'name'     => "Palanquin",
       'label'    => "Palanquin, sans-serif",
       'family'   => "Palanquin, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'palanquin-dark' => array(
       'id'       => 'palanquin-dark',
       'name'     => "Palanquin Dark",
       'label'    => "'Palanquin Dark', sans-serif",
       'family'   => "Palanquin Dark, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'pangolin' => array(
       'id'       => 'pangolin',
       'name'     => "Pangolin",
       'label'    => "Pangolin, handwriting",
       'family'   => "Pangolin, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'paprika' => array(
       'id'       => 'paprika',
       'name'     => "Paprika",
       'label'    => "Paprika, display",
       'family'   => "Paprika, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'parisienne' => array(
       'id'       => 'parisienne',
       'name'     => "Parisienne",
       'label'    => "Parisienne, handwriting",
       'family'   => "Parisienne, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'passero-one' => array(
       'id'       => 'passero-one',
       'name'     => "Passero One",
       'label'    => "'Passero One', display",
       'family'   => "Passero One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'passion-one' => array(
       'id'       => 'passion-one',
       'name'     => "Passion One",
       'label'    => "'Passion One', display",
       'family'   => "Passion One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'pathway-gothic-one' => array(
       'id'       => 'pathway-gothic-one',
       'name'     => "Pathway Gothic One",
       'label'    => "'Pathway Gothic One', sans-serif",
       'family'   => "Pathway Gothic One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'patrick-hand' => array(
       'id'       => 'patrick-hand',
       'name'     => "Patrick Hand",
       'label'    => "'Patrick Hand', handwriting",
       'family'   => "Patrick Hand, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'patrick-hand-sc' => array(
       'id'       => 'patrick-hand-sc',
       'name'     => "Patrick Hand SC",
       'label'    => "'Patrick Hand SC', handwriting",
       'family'   => "Patrick Hand SC, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pattaya' => array(
       'id'       => 'pattaya',
       'name'     => "Pattaya",
       'label'    => "Pattaya, sans-serif",
       'family'   => "Pattaya, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'patua-one' => array(
       'id'       => 'patua-one',
       'name'     => "Patua One",
       'label'    => "'Patua One', display",
       'family'   => "Patua One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pavanam' => array(
       'id'       => 'pavanam',
       'name'     => "Pavanam",
       'label'    => "Pavanam, sans-serif",
       'family'   => "Pavanam, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'tamil'  => 'Tamil',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'paytone-one' => array(
       'id'       => 'paytone-one',
       'name'     => "Paytone One",
       'label'    => "'Paytone One', sans-serif",
       'family'   => "Paytone One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'peddana' => array(
       'id'       => 'peddana',
       'name'     => "Peddana",
       'label'    => "Peddana, serif",
       'family'   => "Peddana, serif",
       'category' => "serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'peralta' => array(
       'id'       => 'peralta',
       'name'     => "Peralta",
       'label'    => "Peralta, display",
       'family'   => "Peralta, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'permanent-marker' => array(
       'id'       => 'permanent-marker',
       'name'     => "Permanent Marker",
       'label'    => "'Permanent Marker', handwriting",
       'family'   => "Permanent Marker, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'petit-formal-script' => array(
       'id'       => 'petit-formal-script',
       'name'     => "Petit Formal Script",
       'label'    => "'Petit Formal Script', handwriting",
       'family'   => "Petit Formal Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'petrona' => array(
       'id'       => 'petrona',
       'name'     => "Petrona",
       'label'    => "Petrona, serif",
       'family'   => "Petrona, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'philosopher' => array(
       'id'       => 'philosopher',
       'name'     => "Philosopher",
       'label'    => "Philosopher, sans-serif",
       'family'   => "Philosopher, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'piedra' => array(
       'id'       => 'piedra',
       'name'     => "Piedra",
       'label'    => "Piedra, display",
       'family'   => "Piedra, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pinyon-script' => array(
       'id'       => 'pinyon-script',
       'name'     => "Pinyon Script",
       'label'    => "'Pinyon Script', handwriting",
       'family'   => "Pinyon Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pirata-one' => array(
       'id'       => 'pirata-one',
       'name'     => "Pirata One",
       'label'    => "'Pirata One', display",
       'family'   => "Pirata One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'plaster' => array(
       'id'       => 'plaster',
       'name'     => "Plaster",
       'label'    => "Plaster, display",
       'family'   => "Plaster, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'play' => array(
       'id'       => 'play',
       'name'     => "Play",
       'label'    => "Play, sans-serif",
       'family'   => "Play, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'playball' => array(
       'id'       => 'playball',
       'name'     => "Playball",
       'label'    => "Playball, display",
       'family'   => "Playball, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'playfair-display' => array(
       'id'       => 'playfair-display',
       'name'     => "Playfair Display",
       'label'    => "'Playfair Display', serif",
       'family'   => "Playfair Display, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'playfair-display-sc' => array(
       'id'       => 'playfair-display-sc',
       'name'     => "Playfair Display SC",
       'label'    => "'Playfair Display SC', serif",
       'family'   => "Playfair Display SC, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'podkova' => array(
       'id'       => 'podkova',
       'name'     => "Podkova",
       'label'    => "Podkova, serif",
       'family'   => "Podkova, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'poiret-one' => array(
       'id'       => 'poiret-one',
       'name'     => "Poiret One",
       'label'    => "'Poiret One', display",
       'family'   => "Poiret One, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'poller-one' => array(
       'id'       => 'poller-one',
       'name'     => "Poller One",
       'label'    => "'Poller One', display",
       'family'   => "Poller One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'poly' => array(
       'id'       => 'poly',
       'name'     => "Poly",
       'label'    => "Poly, serif",
       'family'   => "Poly, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'pompiere' => array(
       'id'       => 'pompiere',
       'name'     => "Pompiere",
       'label'    => "Pompiere, display",
       'family'   => "Pompiere, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pontano-sans' => array(
       'id'       => 'pontano-sans',
       'name'     => "Pontano Sans",
       'label'    => "'Pontano Sans', sans-serif",
       'family'   => "Pontano Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'poppins' => array(
       'id'       => 'poppins',
       'name'     => "Poppins",
       'label'    => "Poppins, sans-serif",
       'family'   => "Poppins, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'port-lligat-sans' => array(
       'id'       => 'port-lligat-sans',
       'name'     => "Port Lligat Sans",
       'label'    => "'Port Lligat Sans', sans-serif",
       'family'   => "Port Lligat Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'port-lligat-slab' => array(
       'id'       => 'port-lligat-slab',
       'name'     => "Port Lligat Slab",
       'label'    => "'Port Lligat Slab', serif",
       'family'   => "Port Lligat Slab, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pragati-narrow' => array(
       'id'       => 'pragati-narrow',
       'name'     => "Pragati Narrow",
       'label'    => "'Pragati Narrow', sans-serif",
       'family'   => "Pragati Narrow, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'prata' => array(
       'id'       => 'prata',
       'name'     => "Prata",
       'label'    => "Prata, serif",
       'family'   => "Prata, serif",
       'category' => "serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'preahvihear' => array(
       'id'       => 'preahvihear',
       'name'     => "Preahvihear",
       'label'    => "Preahvihear, display",
       'family'   => "Preahvihear, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'press-start-2p' => array(
       'id'       => 'press-start-2p',
       'name'     => "Press Start 2P",
       'label'    => "'Press Start 2P', display",
       'family'   => "Press Start 2P, cursive",
       'category' => "display",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'pridi' => array(
       'id'       => 'pridi',
       'name'     => "Pridi",
       'label'    => "Pridi, serif",
       'family'   => "Pridi, serif",
       'category' => "serif",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'princess-sofia' => array(
       'id'       => 'princess-sofia',
       'name'     => "Princess Sofia",
       'label'    => "'Princess Sofia', handwriting",
       'family'   => "Princess Sofia, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'prociono' => array(
       'id'       => 'prociono',
       'name'     => "Prociono",
       'label'    => "Prociono, serif",
       'family'   => "Prociono, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'prompt' => array(
       'id'       => 'prompt',
       'name'     => "Prompt",
       'label'    => "Prompt, sans-serif",
       'family'   => "Prompt, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'prosto-one' => array(
       'id'       => 'prosto-one',
       'name'     => "Prosto One",
       'label'    => "'Prosto One', display",
       'family'   => "Prosto One, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'proza-libre' => array(
       'id'       => 'proza-libre',
       'name'     => "Proza Libre",
       'label'    => "'Proza Libre', sans-serif",
       'family'   => "Proza Libre, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
       ),
  ),

  'puritan' => array(
       'id'       => 'puritan',
       'name'     => "Puritan",
       'label'    => "Puritan, sans-serif",
       'family'   => "Puritan, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'purple-purse' => array(
       'id'       => 'purple-purse',
       'name'     => "Purple Purse",
       'label'    => "'Purple Purse', display",
       'family'   => "Purple Purse, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'quando' => array(
       'id'       => 'quando',
       'name'     => "Quando",
       'label'    => "Quando, serif",
       'family'   => "Quando, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'quantico' => array(
       'id'       => 'quantico',
       'name'     => "Quantico",
       'label'    => "Quantico, sans-serif",
       'family'   => "Quantico, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'quattrocento' => array(
       'id'       => 'quattrocento',
       'name'     => "Quattrocento",
       'label'    => "Quattrocento, serif",
       'family'   => "Quattrocento, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'quattrocento-sans' => array(
       'id'       => 'quattrocento-sans',
       'name'     => "Quattrocento Sans",
       'label'    => "'Quattrocento Sans', sans-serif",
       'family'   => "Quattrocento Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'questrial' => array(
       'id'       => 'questrial',
       'name'     => "Questrial",
       'label'    => "Questrial, sans-serif",
       'family'   => "Questrial, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'quicksand' => array(
       'id'       => 'quicksand',
       'name'     => "Quicksand",
       'label'    => "Quicksand, sans-serif",
       'family'   => "Quicksand, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
       ),
  ),

  'quintessential' => array(
       'id'       => 'quintessential',
       'name'     => "Quintessential",
       'label'    => "Quintessential, handwriting",
       'family'   => "Quintessential, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'qwigley' => array(
       'id'       => 'qwigley',
       'name'     => "Qwigley",
       'label'    => "Qwigley, handwriting",
       'family'   => "Qwigley, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'racing-sans-one' => array(
       'id'       => 'racing-sans-one',
       'name'     => "Racing Sans One",
       'label'    => "'Racing Sans One', display",
       'family'   => "Racing Sans One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'radley' => array(
       'id'       => 'radley',
       'name'     => "Radley",
       'label'    => "Radley, serif",
       'family'   => "Radley, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'rajdhani' => array(
       'id'       => 'rajdhani',
       'name'     => "Rajdhani",
       'label'    => "Rajdhani, sans-serif",
       'family'   => "Rajdhani, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'rakkas' => array(
       'id'       => 'rakkas',
       'name'     => "Rakkas",
       'label'    => "Rakkas, display",
       'family'   => "Rakkas, cursive",
       'category' => "display",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'raleway' => array(
       'id'       => 'raleway',
       'name'     => "Raleway",
       'label'    => "Raleway, sans-serif",
       'family'   => "Raleway, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'raleway-dots' => array(
       'id'       => 'raleway-dots',
       'name'     => "Raleway Dots",
       'label'    => "'Raleway Dots', display",
       'family'   => "Raleway Dots, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ramabhadra' => array(
       'id'       => 'ramabhadra',
       'name'     => "Ramabhadra",
       'label'    => "Ramabhadra, sans-serif",
       'family'   => "Ramabhadra, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ramaraja' => array(
       'id'       => 'ramaraja',
       'name'     => "Ramaraja",
       'label'    => "Ramaraja, serif",
       'family'   => "Ramaraja, serif",
       'category' => "serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rambla' => array(
       'id'       => 'rambla',
       'name'     => "Rambla",
       'label'    => "Rambla, sans-serif",
       'family'   => "Rambla, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'rammetto-one' => array(
       'id'       => 'rammetto-one',
       'name'     => "Rammetto One",
       'label'    => "'Rammetto One', display",
       'family'   => "Rammetto One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ranchers' => array(
       'id'       => 'ranchers',
       'name'     => "Ranchers",
       'label'    => "Ranchers, display",
       'family'   => "Ranchers, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rancho' => array(
       'id'       => 'rancho',
       'name'     => "Rancho",
       'label'    => "Rancho, handwriting",
       'family'   => "Rancho, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ranga' => array(
       'id'       => 'ranga',
       'name'     => "Ranga",
       'label'    => "Ranga, display",
       'family'   => "Ranga, cursive",
       'category' => "display",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'rasa' => array(
       'id'       => 'rasa',
       'name'     => "Rasa",
       'label'    => "Rasa, serif",
       'family'   => "Rasa, serif",
       'category' => "serif",
       'subsets'  => array(
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'rationale' => array(
       'id'       => 'rationale',
       'name'     => "Rationale",
       'label'    => "Rationale, sans-serif",
       'family'   => "Rationale, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ravi-prakash' => array(
       'id'       => 'ravi-prakash',
       'name'     => "Ravi Prakash",
       'label'    => "'Ravi Prakash', display",
       'family'   => "Ravi Prakash, cursive",
       'category' => "display",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'redressed' => array(
       'id'       => 'redressed',
       'name'     => "Redressed",
       'label'    => "Redressed, handwriting",
       'family'   => "Redressed, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'reem-kufi' => array(
       'id'       => 'reem-kufi',
       'name'     => "Reem Kufi",
       'label'    => "'Reem Kufi', sans-serif",
       'family'   => "Reem Kufi, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'reenie-beanie' => array(
       'id'       => 'reenie-beanie',
       'name'     => "Reenie Beanie",
       'label'    => "'Reenie Beanie', handwriting",
       'family'   => "Reenie Beanie, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'revalia' => array(
       'id'       => 'revalia',
       'name'     => "Revalia",
       'label'    => "Revalia, display",
       'family'   => "Revalia, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rhodium-libre' => array(
       'id'       => 'rhodium-libre',
       'name'     => "Rhodium Libre",
       'label'    => "'Rhodium Libre', serif",
       'family'   => "Rhodium Libre, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ribeye' => array(
       'id'       => 'ribeye',
       'name'     => "Ribeye",
       'label'    => "Ribeye, display",
       'family'   => "Ribeye, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ribeye-marrow' => array(
       'id'       => 'ribeye-marrow',
       'name'     => "Ribeye Marrow",
       'label'    => "'Ribeye Marrow', display",
       'family'   => "Ribeye Marrow, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'righteous' => array(
       'id'       => 'righteous',
       'name'     => "Righteous",
       'label'    => "Righteous, display",
       'family'   => "Righteous, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'risque' => array(
       'id'       => 'risque',
       'name'     => "Risque",
       'label'    => "Risque, display",
       'family'   => "Risque, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'roboto' => array(
       'id'       => 'roboto',
       'name'     => "Roboto",
       'label'    => "Roboto, sans-serif",
       'family'   => "Roboto, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'roboto-condensed' => array(
       'id'       => 'roboto-condensed',
       'name'     => "Roboto Condensed",
       'label'    => "'Roboto Condensed', sans-serif",
       'family'   => "Roboto Condensed, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'roboto-mono' => array(
       'id'       => 'roboto-mono',
       'name'     => "Roboto Mono",
       'label'    => "'Roboto Mono', monospace",
       'family'   => "Roboto Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '100i'  =>  'Thin 100 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'roboto-slab' => array(
       'id'       => 'roboto-slab',
       'name'     => "Roboto Slab",
       'label'    => "'Roboto Slab', serif",
       'family'   => "Roboto Slab, serif",
       'category' => "serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'rochester' => array(
       'id'       => 'rochester',
       'name'     => "Rochester",
       'label'    => "Rochester, handwriting",
       'family'   => "Rochester, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rock-salt' => array(
       'id'       => 'rock-salt',
       'name'     => "Rock Salt",
       'label'    => "'Rock Salt', handwriting",
       'family'   => "Rock Salt, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rokkitt' => array(
       'id'       => 'rokkitt',
       'name'     => "Rokkitt",
       'label'    => "Rokkitt, serif",
       'family'   => "Rokkitt, serif",
       'category' => "serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'romanesco' => array(
       'id'       => 'romanesco',
       'name'     => "Romanesco",
       'label'    => "Romanesco, handwriting",
       'family'   => "Romanesco, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ropa-sans' => array(
       'id'       => 'ropa-sans',
       'name'     => "Ropa Sans",
       'label'    => "'Ropa Sans', sans-serif",
       'family'   => "Ropa Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'rosario' => array(
       'id'       => 'rosario',
       'name'     => "Rosario",
       'label'    => "Rosario, sans-serif",
       'family'   => "Rosario, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'rosarivo' => array(
       'id'       => 'rosarivo',
       'name'     => "Rosarivo",
       'label'    => "Rosarivo, serif",
       'family'   => "Rosarivo, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'rouge-script' => array(
       'id'       => 'rouge-script',
       'name'     => "Rouge Script",
       'label'    => "'Rouge Script', handwriting",
       'family'   => "Rouge Script, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rozha-one' => array(
       'id'       => 'rozha-one',
       'name'     => "Rozha One",
       'label'    => "'Rozha One', serif",
       'family'   => "Rozha One, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rubik' => array(
       'id'       => 'rubik',
       'name'     => "Rubik",
       'label'    => "Rubik, sans-serif",
       'family'   => "Rubik, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'rubik-mono-one' => array(
       'id'       => 'rubik-mono-one',
       'name'     => "Rubik Mono One",
       'label'    => "'Rubik Mono One', sans-serif",
       'family'   => "Rubik Mono One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ruda' => array(
       'id'       => 'ruda',
       'name'     => "Ruda",
       'label'    => "Ruda, sans-serif",
       'family'   => "Ruda, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'rufina' => array(
       'id'       => 'rufina',
       'name'     => "Rufina",
       'label'    => "Rufina, serif",
       'family'   => "Rufina, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'ruge-boogie' => array(
       'id'       => 'ruge-boogie',
       'name'     => "Ruge Boogie",
       'label'    => "'Ruge Boogie', handwriting",
       'family'   => "Ruge Boogie, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ruluko' => array(
       'id'       => 'ruluko',
       'name'     => "Ruluko",
       'label'    => "Ruluko, sans-serif",
       'family'   => "Ruluko, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rum-raisin' => array(
       'id'       => 'rum-raisin',
       'name'     => "Rum Raisin",
       'label'    => "'Rum Raisin', sans-serif",
       'family'   => "Rum Raisin, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ruslan-display' => array(
       'id'       => 'ruslan-display',
       'name'     => "Ruslan Display",
       'label'    => "'Ruslan Display', display",
       'family'   => "Ruslan Display, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'russo-one' => array(
       'id'       => 'russo-one',
       'name'     => "Russo One",
       'label'    => "'Russo One', sans-serif",
       'family'   => "Russo One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ruthie' => array(
       'id'       => 'ruthie',
       'name'     => "Ruthie",
       'label'    => "Ruthie, handwriting",
       'family'   => "Ruthie, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'rye' => array(
       'id'       => 'rye',
       'name'     => "Rye",
       'label'    => "Rye, display",
       'family'   => "Rye, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sacramento' => array(
       'id'       => 'sacramento',
       'name'     => "Sacramento",
       'label'    => "Sacramento, handwriting",
       'family'   => "Sacramento, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sahitya' => array(
       'id'       => 'sahitya',
       'name'     => "Sahitya",
       'label'    => "Sahitya, serif",
       'family'   => "Sahitya, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'sail' => array(
       'id'       => 'sail',
       'name'     => "Sail",
       'label'    => "Sail, display",
       'family'   => "Sail, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'salsa' => array(
       'id'       => 'salsa',
       'name'     => "Salsa",
       'label'    => "Salsa, display",
       'family'   => "Salsa, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sanchez' => array(
       'id'       => 'sanchez',
       'name'     => "Sanchez",
       'label'    => "Sanchez, serif",
       'family'   => "Sanchez, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'sancreek' => array(
       'id'       => 'sancreek',
       'name'     => "Sancreek",
       'label'    => "Sancreek, display",
       'family'   => "Sancreek, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sansita' => array(
       'id'       => 'sansita',
       'name'     => "Sansita",
       'label'    => "Sansita, sans-serif",
       'family'   => "Sansita, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'sarala' => array(
       'id'       => 'sarala',
       'name'     => "Sarala",
       'label'    => "Sarala, sans-serif",
       'family'   => "Sarala, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'sarina' => array(
       'id'       => 'sarina',
       'name'     => "Sarina",
       'label'    => "Sarina, display",
       'family'   => "Sarina, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sarpanch' => array(
       'id'       => 'sarpanch',
       'name'     => "Sarpanch",
       'label'    => "Sarpanch, sans-serif",
       'family'   => "Sarpanch, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'satisfy' => array(
       'id'       => 'satisfy',
       'name'     => "Satisfy",
       'label'    => "Satisfy, handwriting",
       'family'   => "Satisfy, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'scada' => array(
       'id'       => 'scada',
       'name'     => "Scada",
       'label'    => "Scada, sans-serif",
       'family'   => "Scada, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'scheherazade' => array(
       'id'       => 'scheherazade',
       'name'     => "Scheherazade",
       'label'    => "Scheherazade, serif",
       'family'   => "Scheherazade, serif",
       'category' => "serif",
       'subsets'  => array(
           'arabic'  => 'Arabic',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'schoolbell' => array(
       'id'       => 'schoolbell',
       'name'     => "Schoolbell",
       'label'    => "Schoolbell, handwriting",
       'family'   => "Schoolbell, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'scope-one' => array(
       'id'       => 'scope-one',
       'name'     => "Scope One",
       'label'    => "'Scope One', serif",
       'family'   => "Scope One, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'seaweed-script' => array(
       'id'       => 'seaweed-script',
       'name'     => "Seaweed Script",
       'label'    => "'Seaweed Script', display",
       'family'   => "Seaweed Script, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'secular-one' => array(
       'id'       => 'secular-one',
       'name'     => "Secular One",
       'label'    => "'Secular One', sans-serif",
       'family'   => "Secular One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sevillana' => array(
       'id'       => 'sevillana',
       'name'     => "Sevillana",
       'label'    => "Sevillana, display",
       'family'   => "Sevillana, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'seymour-one' => array(
       'id'       => 'seymour-one',
       'name'     => "Seymour One",
       'label'    => "'Seymour One', sans-serif",
       'family'   => "Seymour One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'shadows-into-light' => array(
       'id'       => 'shadows-into-light',
       'name'     => "Shadows Into Light",
       'label'    => "'Shadows Into Light', handwriting",
       'family'   => "Shadows Into Light, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'shadows-into-light-two' => array(
       'id'       => 'shadows-into-light-two',
       'name'     => "Shadows Into Light Two",
       'label'    => "'Shadows Into Light Two', handwriting",
       'family'   => "Shadows Into Light Two, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'shanti' => array(
       'id'       => 'shanti',
       'name'     => "Shanti",
       'label'    => "Shanti, sans-serif",
       'family'   => "Shanti, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'share' => array(
       'id'       => 'share',
       'name'     => "Share",
       'label'    => "Share, display",
       'family'   => "Share, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'share-tech' => array(
       'id'       => 'share-tech',
       'name'     => "Share Tech",
       'label'    => "'Share Tech', sans-serif",
       'family'   => "Share Tech, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'share-tech-mono' => array(
       'id'       => 'share-tech-mono',
       'name'     => "Share Tech Mono",
       'label'    => "'Share Tech Mono', monospace",
       'family'   => "Share Tech Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'shojumaru' => array(
       'id'       => 'shojumaru',
       'name'     => "Shojumaru",
       'label'    => "Shojumaru, display",
       'family'   => "Shojumaru, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'short-stack' => array(
       'id'       => 'short-stack',
       'name'     => "Short Stack",
       'label'    => "'Short Stack', handwriting",
       'family'   => "Short Stack, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'shrikhand' => array(
       'id'       => 'shrikhand',
       'name'     => "Shrikhand",
       'label'    => "Shrikhand, display",
       'family'   => "Shrikhand, cursive",
       'category' => "display",
       'subsets'  => array(
           'gujarati'  => 'Gujarati',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'siemreap' => array(
       'id'       => 'siemreap',
       'name'     => "Siemreap",
       'label'    => "Siemreap, display",
       'family'   => "Siemreap, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sigmar-one' => array(
       'id'       => 'sigmar-one',
       'name'     => "Sigmar One",
       'label'    => "'Sigmar One', display",
       'family'   => "Sigmar One, cursive",
       'category' => "display",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'signika' => array(
       'id'       => 'signika',
       'name'     => "Signika",
       'label'    => "Signika, sans-serif",
       'family'   => "Signika, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'signika-negative' => array(
       'id'       => 'signika-negative',
       'name'     => "Signika Negative",
       'label'    => "'Signika Negative', sans-serif",
       'family'   => "Signika Negative, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'simonetta' => array(
       'id'       => 'simonetta',
       'name'     => "Simonetta",
       'label'    => "Simonetta, display",
       'family'   => "Simonetta, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '900'  =>  'Black 900',
           '400i'  =>  'Regular 400 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'sintony' => array(
       'id'       => 'sintony',
       'name'     => "Sintony",
       'label'    => "Sintony, sans-serif",
       'family'   => "Sintony, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'sirin-stencil' => array(
       'id'       => 'sirin-stencil',
       'name'     => "Sirin Stencil",
       'label'    => "'Sirin Stencil', display",
       'family'   => "Sirin Stencil, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'six-caps' => array(
       'id'       => 'six-caps',
       'name'     => "Six Caps",
       'label'    => "'Six Caps', sans-serif",
       'family'   => "Six Caps, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'skranji' => array(
       'id'       => 'skranji',
       'name'     => "Skranji",
       'label'    => "Skranji, display",
       'family'   => "Skranji, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'slabo-13px' => array(
       'id'       => 'slabo-13px',
       'name'     => "Slabo 13px",
       'label'    => "'Slabo 13px', serif",
       'family'   => "Slabo 13px, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'slabo-27px' => array(
       'id'       => 'slabo-27px',
       'name'     => "Slabo 27px",
       'label'    => "'Slabo 27px', serif",
       'family'   => "Slabo 27px, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'slackey' => array(
       'id'       => 'slackey',
       'name'     => "Slackey",
       'label'    => "Slackey, display",
       'family'   => "Slackey, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'smokum' => array(
       'id'       => 'smokum',
       'name'     => "Smokum",
       'label'    => "Smokum, display",
       'family'   => "Smokum, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'smythe' => array(
       'id'       => 'smythe',
       'name'     => "Smythe",
       'label'    => "Smythe, display",
       'family'   => "Smythe, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sniglet' => array(
       'id'       => 'sniglet',
       'name'     => "Sniglet",
       'label'    => "Sniglet, display",
       'family'   => "Sniglet, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'snippet' => array(
       'id'       => 'snippet',
       'name'     => "Snippet",
       'label'    => "Snippet, sans-serif",
       'family'   => "Snippet, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'snowburst-one' => array(
       'id'       => 'snowburst-one',
       'name'     => "Snowburst One",
       'label'    => "'Snowburst One', display",
       'family'   => "Snowburst One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sofadi-one' => array(
       'id'       => 'sofadi-one',
       'name'     => "Sofadi One",
       'label'    => "'Sofadi One', display",
       'family'   => "Sofadi One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sofia' => array(
       'id'       => 'sofia',
       'name'     => "Sofia",
       'label'    => "Sofia, handwriting",
       'family'   => "Sofia, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sonsie-one' => array(
       'id'       => 'sonsie-one',
       'name'     => "Sonsie One",
       'label'    => "'Sonsie One', display",
       'family'   => "Sonsie One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sorts-mill-goudy' => array(
       'id'       => 'sorts-mill-goudy',
       'name'     => "Sorts Mill Goudy",
       'label'    => "'Sorts Mill Goudy', serif",
       'family'   => "Sorts Mill Goudy, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'source-code-pro' => array(
       'id'       => 'source-code-pro',
       'name'     => "Source Code Pro",
       'label'    => "'Source Code Pro', monospace",
       'family'   => "Source Code Pro, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'source-sans-pro' => array(
       'id'       => 'source-sans-pro',
       'name'     => "Source Sans Pro",
       'label'    => "'Source Sans Pro', sans-serif",
       'family'   => "Source Sans Pro, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'source-serif-pro' => array(
       'id'       => 'source-serif-pro',
       'name'     => "Source Serif Pro",
       'label'    => "'Source Serif Pro', serif",
       'family'   => "Source Serif Pro, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'space-mono' => array(
       'id'       => 'space-mono',
       'name'     => "Space Mono",
       'label'    => "'Space Mono', monospace",
       'family'   => "Space Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'special-elite' => array(
       'id'       => 'special-elite',
       'name'     => "Special Elite",
       'label'    => "'Special Elite', display",
       'family'   => "Special Elite, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'spicy-rice' => array(
       'id'       => 'spicy-rice',
       'name'     => "Spicy Rice",
       'label'    => "'Spicy Rice', display",
       'family'   => "Spicy Rice, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'spinnaker' => array(
       'id'       => 'spinnaker',
       'name'     => "Spinnaker",
       'label'    => "Spinnaker, sans-serif",
       'family'   => "Spinnaker, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'spirax' => array(
       'id'       => 'spirax',
       'name'     => "Spirax",
       'label'    => "Spirax, display",
       'family'   => "Spirax, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'squada-one' => array(
       'id'       => 'squada-one',
       'name'     => "Squada One",
       'label'    => "'Squada One', display",
       'family'   => "Squada One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sree-krushnadevaraya' => array(
       'id'       => 'sree-krushnadevaraya',
       'name'     => "Sree Krushnadevaraya",
       'label'    => "'Sree Krushnadevaraya', serif",
       'family'   => "Sree Krushnadevaraya, serif",
       'category' => "serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sriracha' => array(
       'id'       => 'sriracha',
       'name'     => "Sriracha",
       'label'    => "Sriracha, handwriting",
       'family'   => "Sriracha, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'stalemate' => array(
       'id'       => 'stalemate',
       'name'     => "Stalemate",
       'label'    => "Stalemate, handwriting",
       'family'   => "Stalemate, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'stalinist-one' => array(
       'id'       => 'stalinist-one',
       'name'     => "Stalinist One",
       'label'    => "'Stalinist One', display",
       'family'   => "Stalinist One, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'stardos-stencil' => array(
       'id'       => 'stardos-stencil',
       'name'     => "Stardos Stencil",
       'label'    => "'Stardos Stencil', display",
       'family'   => "Stardos Stencil, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'stint-ultra-condensed' => array(
       'id'       => 'stint-ultra-condensed',
       'name'     => "Stint Ultra Condensed",
       'label'    => "'Stint Ultra Condensed', display",
       'family'   => "Stint Ultra Condensed, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'stint-ultra-expanded' => array(
       'id'       => 'stint-ultra-expanded',
       'name'     => "Stint Ultra Expanded",
       'label'    => "'Stint Ultra Expanded', display",
       'family'   => "Stint Ultra Expanded, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'stoke' => array(
       'id'       => 'stoke',
       'name'     => "Stoke",
       'label'    => "Stoke, serif",
       'family'   => "Stoke, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
       ),
  ),

  'strait' => array(
       'id'       => 'strait',
       'name'     => "Strait",
       'label'    => "Strait, sans-serif",
       'family'   => "Strait, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sue-ellen-francisco' => array(
       'id'       => 'sue-ellen-francisco',
       'name'     => "Sue Ellen Francisco",
       'label'    => "'Sue Ellen Francisco', handwriting",
       'family'   => "Sue Ellen Francisco, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'suez-one' => array(
       'id'       => 'suez-one',
       'name'     => "Suez One",
       'label'    => "'Suez One', serif",
       'family'   => "Suez One, serif",
       'category' => "serif",
       'subsets'  => array(
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sumana' => array(
       'id'       => 'sumana',
       'name'     => "Sumana",
       'label'    => "Sumana, serif",
       'family'   => "Sumana, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'sunshiney' => array(
       'id'       => 'sunshiney',
       'name'     => "Sunshiney",
       'label'    => "Sunshiney, handwriting",
       'family'   => "Sunshiney, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'supermercado-one' => array(
       'id'       => 'supermercado-one',
       'name'     => "Supermercado One",
       'label'    => "'Supermercado One', display",
       'family'   => "Supermercado One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'sura' => array(
       'id'       => 'sura',
       'name'     => "Sura",
       'label'    => "Sura, serif",
       'family'   => "Sura, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'suranna' => array(
       'id'       => 'suranna',
       'name'     => "Suranna",
       'label'    => "Suranna, serif",
       'family'   => "Suranna, serif",
       'category' => "serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'suravaram' => array(
       'id'       => 'suravaram',
       'name'     => "Suravaram",
       'label'    => "Suravaram, serif",
       'family'   => "Suravaram, serif",
       'category' => "serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'suwannaphum' => array(
       'id'       => 'suwannaphum',
       'name'     => "Suwannaphum",
       'label'    => "Suwannaphum, display",
       'family'   => "Suwannaphum, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'swanky-and-moo-moo' => array(
       'id'       => 'swanky-and-moo-moo',
       'name'     => "Swanky and Moo Moo",
       'label'    => "'Swanky and Moo Moo', handwriting",
       'family'   => "Swanky and Moo Moo, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'syncopate' => array(
       'id'       => 'syncopate',
       'name'     => "Syncopate",
       'label'    => "Syncopate, sans-serif",
       'family'   => "Syncopate, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'tangerine' => array(
       'id'       => 'tangerine',
       'name'     => "Tangerine",
       'label'    => "Tangerine, handwriting",
       'family'   => "Tangerine, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'taprom' => array(
       'id'       => 'taprom',
       'name'     => "Taprom",
       'label'    => "Taprom, display",
       'family'   => "Taprom, cursive",
       'category' => "display",
       'subsets'  => array(
           'khmer'  => 'Khmer',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'tauri' => array(
       'id'       => 'tauri',
       'name'     => "Tauri",
       'label'    => "Tauri, sans-serif",
       'family'   => "Tauri, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'taviraj' => array(
       'id'       => 'taviraj',
       'name'     => "Taviraj",
       'label'    => "Taviraj, serif",
       'family'   => "Taviraj, serif",
       'category' => "serif",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'teko' => array(
       'id'       => 'teko',
       'name'     => "Teko",
       'label'    => "Teko, sans-serif",
       'family'   => "Teko, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'telex' => array(
       'id'       => 'telex',
       'name'     => "Telex",
       'label'    => "Telex, sans-serif",
       'family'   => "Telex, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'tenali-ramakrishna' => array(
       'id'       => 'tenali-ramakrishna',
       'name'     => "Tenali Ramakrishna",
       'label'    => "'Tenali Ramakrishna', sans-serif",
       'family'   => "Tenali Ramakrishna, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'tenor-sans' => array(
       'id'       => 'tenor-sans',
       'name'     => "Tenor Sans",
       'label'    => "'Tenor Sans', sans-serif",
       'family'   => "Tenor Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'text-me-one' => array(
       'id'       => 'text-me-one',
       'name'     => "Text Me One",
       'label'    => "'Text Me One', sans-serif",
       'family'   => "Text Me One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'the-girl-next-door' => array(
       'id'       => 'the-girl-next-door',
       'name'     => "The Girl Next Door",
       'label'    => "'The Girl Next Door', handwriting",
       'family'   => "The Girl Next Door, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'tienne' => array(
       'id'       => 'tienne',
       'name'     => "Tienne",
       'label'    => "Tienne, serif",
       'family'   => "Tienne, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'tillana' => array(
       'id'       => 'tillana',
       'name'     => "Tillana",
       'label'    => "Tillana, handwriting",
       'family'   => "Tillana, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
       ),
  ),

  'timmana' => array(
       'id'       => 'timmana',
       'name'     => "Timmana",
       'label'    => "Timmana, sans-serif",
       'family'   => "Timmana, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'telugu'  => 'Telugu',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'tinos' => array(
       'id'       => 'tinos',
       'name'     => "Tinos",
       'label'    => "Tinos, serif",
       'family'   => "Tinos, serif",
       'category' => "serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'titan-one' => array(
       'id'       => 'titan-one',
       'name'     => "Titan One",
       'label'    => "'Titan One', display",
       'family'   => "Titan One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'titillium-web' => array(
       'id'       => 'titillium-web',
       'name'     => "Titillium Web",
       'label'    => "'Titillium Web', sans-serif",
       'family'   => "Titillium Web, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'trade-winds' => array(
       'id'       => 'trade-winds',
       'name'     => "Trade Winds",
       'label'    => "'Trade Winds', display",
       'family'   => "Trade Winds, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'trirong' => array(
       'id'       => 'trirong',
       'name'     => "Trirong",
       'label'    => "Trirong, serif",
       'family'   => "Trirong, serif",
       'category' => "serif",
       'subsets'  => array(
           'thai'  => 'Thai',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
           '100i'  =>  'Thin 100 Italic',
           '200i'  =>  'Extra light 200 Italic',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '600i'  =>  'Semi bold 600 Italic',
           '700i'  =>  'Bold 700 Italic',
           '800i'  =>  'Extra bold 800 Italic',
           '900i'  =>  'Black 900 Italic',
       ),
  ),

  'trocchi' => array(
       'id'       => 'trocchi',
       'name'     => "Trocchi",
       'label'    => "Trocchi, serif",
       'family'   => "Trocchi, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'trochut' => array(
       'id'       => 'trochut',
       'name'     => "Trochut",
       'label'    => "Trochut, display",
       'family'   => "Trochut, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
       ),
  ),

  'trykker' => array(
       'id'       => 'trykker',
       'name'     => "Trykker",
       'label'    => "Trykker, serif",
       'family'   => "Trykker, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'tulpen-one' => array(
       'id'       => 'tulpen-one',
       'name'     => "Tulpen One",
       'label'    => "'Tulpen One', display",
       'family'   => "Tulpen One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ubuntu' => array(
       'id'       => 'ubuntu',
       'name'     => "Ubuntu",
       'label'    => "Ubuntu, sans-serif",
       'family'   => "Ubuntu, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '300i'  =>  'Light 300 Italic',
           '400i'  =>  'Regular 400 Italic',
           '500i'  =>  'Medium 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'ubuntu-condensed' => array(
       'id'       => 'ubuntu-condensed',
       'name'     => "Ubuntu Condensed",
       'label'    => "'Ubuntu Condensed', sans-serif",
       'family'   => "Ubuntu Condensed, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'ubuntu-mono' => array(
       'id'       => 'ubuntu-mono',
       'name'     => "Ubuntu Mono",
       'label'    => "'Ubuntu Mono', monospace",
       'family'   => "Ubuntu Mono, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'greek'  => 'Greek',
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'greek-ext'  => 'Greek Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'ultra' => array(
       'id'       => 'ultra',
       'name'     => "Ultra",
       'label'    => "Ultra, serif",
       'family'   => "Ultra, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'uncial-antiqua' => array(
       'id'       => 'uncial-antiqua',
       'name'     => "Uncial Antiqua",
       'label'    => "'Uncial Antiqua', display",
       'family'   => "Uncial Antiqua, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'underdog' => array(
       'id'       => 'underdog',
       'name'     => "Underdog",
       'label'    => "Underdog, display",
       'family'   => "Underdog, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'unica-one' => array(
       'id'       => 'unica-one',
       'name'     => "Unica One",
       'label'    => "'Unica One', display",
       'family'   => "Unica One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'unifrakturcook' => array(
       'id'       => 'unifrakturcook',
       'name'     => "UnifrakturCook",
       'label'    => "UnifrakturCook, display",
       'family'   => "UnifrakturCook, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '700'  =>  'Bold 700',
       ),
  ),

  'unifrakturmaguntia' => array(
       'id'       => 'unifrakturmaguntia',
       'name'     => "UnifrakturMaguntia",
       'label'    => "UnifrakturMaguntia, display",
       'family'   => "UnifrakturMaguntia, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'unkempt' => array(
       'id'       => 'unkempt',
       'name'     => "Unkempt",
       'label'    => "Unkempt, display",
       'family'   => "Unkempt, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'unlock' => array(
       'id'       => 'unlock',
       'name'     => "Unlock",
       'label'    => "Unlock, display",
       'family'   => "Unlock, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'unna' => array(
       'id'       => 'unna',
       'name'     => "Unna",
       'label'    => "Unna, serif",
       'family'   => "Unna, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'vt323' => array(
       'id'       => 'vt323',
       'name'     => "VT323",
       'label'    => "VT323, monospace",
       'family'   => "VT323, monospace",
       'category' => "monospace",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'vampiro-one' => array(
       'id'       => 'vampiro-one',
       'name'     => "Vampiro One",
       'label'    => "'Vampiro One', display",
       'family'   => "Vampiro One, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'varela' => array(
       'id'       => 'varela',
       'name'     => "Varela",
       'label'    => "Varela, sans-serif",
       'family'   => "Varela, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'varela-round' => array(
       'id'       => 'varela-round',
       'name'     => "Varela Round",
       'label'    => "'Varela Round', sans-serif",
       'family'   => "Varela Round, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'vietnamese'  => 'Vietnamese',
           'hebrew'  => 'Hebrew',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'vast-shadow' => array(
       'id'       => 'vast-shadow',
       'name'     => "Vast Shadow",
       'label'    => "'Vast Shadow', display",
       'family'   => "Vast Shadow, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'vesper-libre' => array(
       'id'       => 'vesper-libre',
       'name'     => "Vesper Libre",
       'label'    => "'Vesper Libre', serif",
       'family'   => "Vesper Libre, serif",
       'category' => "serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'vibur' => array(
       'id'       => 'vibur',
       'name'     => "Vibur",
       'label'    => "Vibur, handwriting",
       'family'   => "Vibur, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'vidaloka' => array(
       'id'       => 'vidaloka',
       'name'     => "Vidaloka",
       'label'    => "Vidaloka, serif",
       'family'   => "Vidaloka, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'viga' => array(
       'id'       => 'viga',
       'name'     => "Viga",
       'label'    => "Viga, sans-serif",
       'family'   => "Viga, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'voces' => array(
       'id'       => 'voces',
       'name'     => "Voces",
       'label'    => "Voces, display",
       'family'   => "Voces, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'volkhov' => array(
       'id'       => 'volkhov',
       'name'     => "Volkhov",
       'label'    => "Volkhov, serif",
       'family'   => "Volkhov, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'vollkorn' => array(
       'id'       => 'vollkorn',
       'name'     => "Vollkorn",
       'label'    => "Vollkorn, serif",
       'family'   => "Vollkorn, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
           '400i'  =>  'Regular 400 Italic',
           '700i'  =>  'Bold 700 Italic',
       ),
  ),

  'voltaire' => array(
       'id'       => 'voltaire',
       'name'     => "Voltaire",
       'label'    => "Voltaire, sans-serif",
       'family'   => "Voltaire, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'waiting-for-the-sunrise' => array(
       'id'       => 'waiting-for-the-sunrise',
       'name'     => "Waiting for the Sunrise",
       'label'    => "'Waiting for the Sunrise', handwriting",
       'family'   => "Waiting for the Sunrise, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'wallpoet' => array(
       'id'       => 'wallpoet',
       'name'     => "Wallpoet",
       'label'    => "Wallpoet, display",
       'family'   => "Wallpoet, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'walter-turncoat' => array(
       'id'       => 'walter-turncoat',
       'name'     => "Walter Turncoat",
       'label'    => "'Walter Turncoat', handwriting",
       'family'   => "Walter Turncoat, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'warnes' => array(
       'id'       => 'warnes',
       'name'     => "Warnes",
       'label'    => "Warnes, display",
       'family'   => "Warnes, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'wellfleet' => array(
       'id'       => 'wellfleet',
       'name'     => "Wellfleet",
       'label'    => "Wellfleet, display",
       'family'   => "Wellfleet, cursive",
       'category' => "display",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'wendy-one' => array(
       'id'       => 'wendy-one',
       'name'     => "Wendy One",
       'label'    => "'Wendy One', sans-serif",
       'family'   => "Wendy One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'wire-one' => array(
       'id'       => 'wire-one',
       'name'     => "Wire One",
       'label'    => "'Wire One', sans-serif",
       'family'   => "Wire One, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'work-sans' => array(
       'id'       => 'work-sans',
       'name'     => "Work Sans",
       'label'    => "'Work Sans', sans-serif",
       'family'   => "Work Sans, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
           '800'  =>  'Extra bold 800',
           '900'  =>  'Black 900',
       ),
  ),

  'yanone-kaffeesatz' => array(
       'id'       => 'yanone-kaffeesatz',
       'name'     => "Yanone Kaffeesatz",
       'label'    => "'Yanone Kaffeesatz', sans-serif",
       'family'   => "Yanone Kaffeesatz, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '200'  =>  'Extra light 200',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '700'  =>  'Bold 700',
       ),
  ),

  'yantramanav' => array(
       'id'       => 'yantramanav',
       'name'     => "Yantramanav",
       'label'    => "Yantramanav, sans-serif",
       'family'   => "Yantramanav, sans-serif",
       'category' => "sans-serif",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '100'  =>  'Thin 100',
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '700'  =>  'Bold 700',
           '900'  =>  'Black 900',
       ),
  ),

  'yatra-one' => array(
       'id'       => 'yatra-one',
       'name'     => "Yatra One",
       'label'    => "'Yatra One', display",
       'family'   => "Yatra One, cursive",
       'category' => "display",
       'subsets'  => array(
           'devanagari'  => 'Devanagari',
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'yellowtail' => array(
       'id'       => 'yellowtail',
       'name'     => "Yellowtail",
       'label'    => "Yellowtail, handwriting",
       'family'   => "Yellowtail, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'yeseva-one' => array(
       'id'       => 'yeseva-one',
       'name'     => "Yeseva One",
       'label'    => "'Yeseva One', display",
       'family'   => "Yeseva One, cursive",
       'category' => "display",
       'subsets'  => array(
           'cyrillic'  => 'Cyrillic',
           'vietnamese'  => 'Vietnamese',
           'latin-ext'  => 'Latin Extended',
           'cyrillic-ext'  => 'Cyrillic Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'yesteryear' => array(
       'id'       => 'yesteryear',
       'name'     => "Yesteryear",
       'label'    => "Yesteryear, handwriting",
       'family'   => "Yesteryear, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

  'yrsa' => array(
       'id'       => 'yrsa',
       'name'     => "Yrsa",
       'label'    => "Yrsa, serif",
       'family'   => "Yrsa, serif",
       'category' => "serif",
       'subsets'  => array(
           'latin-ext'  => 'Latin Extended',
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '300'  =>  'Light 300',
           '400'  =>  'Regular 400',
           '500'  =>  'Medium 500',
           '600'  =>  'Semi bold 600',
           '700'  =>  'Bold 700',
       ),
  ),

  'zeyada' => array(
       'id'       => 'zeyada',
       'name'     => "Zeyada",
       'label'    => "Zeyada, handwriting",
       'family'   => "Zeyada, cursive",
       'category' => "handwriting",
       'subsets'  => array(
           'latin'  => 'Latin',
       ),
       'variants'  => array(
           '400'  =>  'Regular 400',
       ),
  ),

);